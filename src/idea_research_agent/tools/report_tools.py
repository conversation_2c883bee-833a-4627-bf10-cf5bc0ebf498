"""
Report generation tools for the Multi-Agent Idea Generation System.

These tools format and export final business ideas according to PRD specifications.
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, List
from google.adk.tools import BaseTool
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MarkdownReportTool(BaseTool):
    """
    Tool for generating standardized markdown reports as specified in the PRD.
    """
    
    def __init__(self):
        super().__init__(
            name="markdown_report",
            description="Generate standardized markdown documentation for finalized business ideas",
            parameters={
                "idea": {
                    "type": "object",
                    "description": "Finalized business idea with all required fields"
                },
                "evaluation": {
                    "type": "object",
                    "description": "Evaluation results from critique process"
                },
                "market_research": {
                    "type": "object",
                    "description": "Market research data supporting the idea",
                    "default": {}
                }
            }
        )
    
    def execute(self, idea: Dict[str, Any], evaluation: Dict[str, Any], market_research: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate a standardized markdown report for a business idea.
        
        Args:
            idea: Business idea with title, description, etc.
            evaluation: Evaluation scores and feedback
            market_research: Supporting market data
            
        Returns:
            Dict containing the generated markdown report
        """
        try:
            if market_research is None:
                market_research = {}
            
            # Generate markdown content according to PRD format
            markdown_content = self._generate_markdown(idea, evaluation, market_research)
            
            # Generate filename
            title_safe = "".join(c for c in idea.get("title", "idea") if c.isalnum() or c in (' ', '-', '_')).rstrip()
            title_safe = title_safe.replace(' ', '_').lower()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{title_safe}_{timestamp}.md"
            
            return {
                "markdown_content": markdown_content,
                "filename": filename,
                "word_count": len(markdown_content.split()),
                "sections": ["use_case", "impact", "complexity", "uniqueness", "data_required"],
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Markdown report generation failed: {str(e)}")
            return {
                "error": str(e),
                "markdown_content": "",
                "filename": "error_report.md"
            }
    
    def _generate_markdown(self, idea: Dict[str, Any], evaluation: Dict[str, Any], market_research: Dict[str, Any]) -> str:
        """Generate the actual markdown content according to PRD specifications."""
        
        title = idea.get("title", "Business Idea")
        description = idea.get("description", "No description provided")
        
        # Extract evaluation scores
        overall_score = evaluation.get("overall_score", 0.0)
        practicality_score = evaluation.get("practicality", {}).get("score", 0.0)
        business_impact_score = evaluation.get("business_impact", {}).get("score", 0.0)
        market_validation_score = evaluation.get("market_validation", {}).get("score", 0.0)
        
        markdown = f"""# {title}

*Generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*

## Executive Summary

{description}

**Overall Evaluation Score:** {overall_score:.2f}/1.0

---

## Use Case

### Problem Statement
{idea.get("problem_statement", "Addresses inefficiencies in current business processes")}

### Target Audience
{idea.get("target_audience", "Business professionals and organizations seeking productivity improvements")}

### Application Scenarios
{idea.get("application_scenarios", "- Daily workflow optimization\\n- Process automation\\n- Employee productivity enhancement")}

---

## Impact

### Quantifiable Benefits
- **Productivity Gains:** {idea.get("productivity_gains", "20-30% improvement in task efficiency")}
- **Cost Savings:** {idea.get("cost_savings", "Reduced operational overhead")}
- **Time Savings:** {idea.get("time_savings", "Significant reduction in manual work")}

### Expected ROI
{idea.get("expected_roi", "Positive ROI within 6-12 months of implementation")}

### Success Metrics
{idea.get("success_metrics", "- User adoption rate\\n- Task completion time\\n- Employee satisfaction scores")}

---

## Complexity

### Implementation Difficulty
**Assessment:** {self._get_complexity_level(practicality_score)}

### Resource Requirements
{idea.get("resource_requirements", "- Development team: 2-4 developers\\n- Timeline: 3-6 months\\n- Budget: Moderate investment required")}

### Technical Dependencies
{idea.get("technical_dependencies", "- Modern web technologies\\n- Cloud infrastructure\\n- Integration capabilities")}

---

## Uniqueness

### Competitive Differentiation
{idea.get("competitive_differentiation", "Novel approach to solving common business challenges")}

### Market Positioning Advantages
- **Innovation Factor:** {market_validation_score:.2f}/1.0
- **Market Gap:** {idea.get("market_gap", "Addresses underserved market needs")}
- **Scalability:** {idea.get("scalability", "Highly scalable across different business sizes")}

---

## Data Required to Begin

### Essential Datasets
{idea.get("essential_datasets", "- User behavior data\\n- Process efficiency metrics\\n- Market research data")}

### Information Sources
{idea.get("information_sources", "- Internal business analytics\\n- Industry reports\\n- User feedback surveys")}

### Initial Setup Requirements
{idea.get("setup_requirements", "- Data collection infrastructure\\n- Analytics platform\\n- User onboarding system")}

---

## Evaluation Details

### Practicality Score: {practicality_score:.2f}/1.0
- **Simplicity:** {evaluation.get("practicality", {}).get("simplicity", 0.0):.2f}
- **Effectiveness:** {evaluation.get("practicality", {}).get("effectiveness", 0.0):.2f}
- **Implementability:** {evaluation.get("practicality", {}).get("implementability", 0.0):.2f}

### Business Impact Score: {business_impact_score:.2f}/1.0
- **Productivity Enhancement:** {evaluation.get("business_impact", {}).get("productivity_enhancement", 0.0):.2f}
- **Task Optimization:** {evaluation.get("business_impact", {}).get("task_optimization", 0.0):.2f}
- **Daily Utility:** {evaluation.get("business_impact", {}).get("daily_utility", 0.0):.2f}

### Market Validation Score: {market_validation_score:.2f}/1.0
- **Credibility:** {evaluation.get("market_validation", {}).get("credibility", 0.0):.2f}
- **Uniqueness:** {evaluation.get("market_validation", {}).get("uniqueness", 0.0):.2f}
- **Adaptability:** {evaluation.get("market_validation", {}).get("adaptability", 0.0):.2f}

---

## Market Research Summary

{self._format_market_research(market_research)}

---

## Improvement Suggestions

{self._format_suggestions(evaluation.get("improvement_suggestions", []))}

---

*This report was generated by the Multi-Agent Idea Generation System*
"""
        
        return markdown
    
    def _get_complexity_level(self, practicality_score: float) -> str:
        """Determine complexity level based on practicality score."""
        if practicality_score >= 0.8:
            return "Low - Straightforward implementation"
        elif practicality_score >= 0.6:
            return "Medium - Moderate complexity"
        else:
            return "High - Complex implementation required"
    
    def _format_market_research(self, market_research: Dict[str, Any]) -> str:
        """Format market research data for the report."""
        if not market_research or not market_research.get("results"):
            return "No specific market research data available for this idea."
        
        results = market_research.get("results", [])[:3]  # Top 3 results
        formatted = "### Key Market Insights\n\n"
        
        for i, result in enumerate(results, 1):
            formatted += f"{i}. **{result.get('title', 'Market Insight')}**\n"
            formatted += f"   - Source: {result.get('displayLink', 'Unknown')}\n"
            formatted += f"   - Summary: {result.get('snippet', 'No summary available')}\n\n"
        
        return formatted
    
    def _format_suggestions(self, suggestions: List[str]) -> str:
        """Format improvement suggestions as a bulleted list."""
        if not suggestions:
            return "No specific improvement suggestions - idea meets quality standards."
        
        formatted = ""
        for suggestion in suggestions:
            formatted += f"- {suggestion}\n"
        
        return formatted


class ExportTool(BaseTool):
    """Tool for exporting reports to files."""
    
    def __init__(self):
        super().__init__(
            name="export_report",
            description="Export generated reports to files in specified directory",
            parameters={
                "content": {
                    "type": "string",
                    "description": "Content to export"
                },
                "filename": {
                    "type": "string", 
                    "description": "Filename for the export"
                },
                "output_dir": {
                    "type": "string",
                    "description": "Output directory path",
                    "default": "./artifacts"
                }
            }
        )
    
    def execute(self, content: str, filename: str, output_dir: str = "./artifacts") -> Dict[str, Any]:
        """
        Export content to a file.
        
        Args:
            content: Content to write to file
            filename: Name of the output file
            output_dir: Directory to save the file
            
        Returns:
            Dict containing export results
        """
        try:
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            
            # Full file path
            file_path = os.path.join(output_dir, filename)
            
            # Write content to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            file_size = os.path.getsize(file_path)
            
            return {
                "success": True,
                "file_path": file_path,
                "filename": filename,
                "file_size_bytes": file_size,
                "exported_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Export failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "file_path": None
            }


# Tool instances for export
markdown_report_tool = MarkdownReportTool()
export_tool = ExportTool()
visualization_tool = None  # To be implemented if needed
