"""
Analysis tools for the Multi-Agent Idea Generation System.

These tools provide analysis capabilities for evaluating and refining business ideas
according to the PRD evaluation metrics.
"""

import json
from typing import List, Dict, Any, Optional
from google.adk.tools import BaseTool
from ..utils.logger import get_logger

logger = get_logger(__name__)


class IdeaEvaluationTool(BaseTool):
    """
    Tool for evaluating business ideas against PRD criteria.
    Implements the comprehensive metrics from the PRD.
    """
    
    def __init__(self):
        super().__init__(
            name="idea_evaluation",
            description="Evaluate business ideas against PRD criteria including practicality, business impact, and market validation",
            parameters={
                "idea": {
                    "type": "object",
                    "description": "Business idea to evaluate with title, description, and details"
                },
                "market_data": {
                    "type": "object", 
                    "description": "Market research data for validation",
                    "default": {}
                }
            }
        )
    
    def execute(self, idea: Dict[str, Any], market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Evaluate a business idea against PRD criteria.
        
        Args:
            idea: Business idea with title, description, target_audience, etc.
            market_data: Optional market research data for validation
            
        Returns:
            Dict containing evaluation scores and feedback
        """
        try:
            if market_data is None:
                market_data = {}
            
            # PRD Evaluation Metrics
            evaluation = {
                "idea_title": idea.get("title", "Untitled Idea"),
                "practicality": self._evaluate_practicality(idea),
                "business_impact": self._evaluate_business_impact(idea),
                "market_validation": self._evaluate_market_validation(idea, market_data),
                "quality_assurance": self._evaluate_quality_assurance(idea),
            }
            
            # Calculate overall score
            scores = [
                evaluation["practicality"]["score"],
                evaluation["business_impact"]["score"], 
                evaluation["market_validation"]["score"],
                evaluation["quality_assurance"]["score"]
            ]
            evaluation["overall_score"] = sum(scores) / len(scores)
            evaluation["meets_threshold"] = evaluation["overall_score"] >= 0.8
            
            # Generate improvement suggestions
            evaluation["improvement_suggestions"] = self._generate_suggestions(evaluation)
            
            return evaluation
            
        except Exception as e:
            logger.error(f"Idea evaluation failed: {str(e)}")
            return {
                "error": str(e),
                "overall_score": 0.0,
                "meets_threshold": False
            }
    
    def _evaluate_practicality(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate practicality criteria: Simplicity, Effectiveness, Implementability."""
        description = idea.get("description", "").lower()
        
        # Simple scoring based on keywords and description length
        simplicity_score = 0.8 if len(description.split()) < 100 else 0.6
        effectiveness_score = 0.9 if "value" in description or "benefit" in description else 0.7
        implementability_score = 0.8 if "technology" in description or "feasible" in description else 0.6
        
        score = (simplicity_score + effectiveness_score + implementability_score) / 3
        
        return {
            "score": score,
            "simplicity": simplicity_score,
            "effectiveness": effectiveness_score, 
            "implementability": implementability_score,
            "feedback": "Idea demonstrates good practicality" if score > 0.7 else "Needs improvement in practical aspects"
        }
    
    def _evaluate_business_impact(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate business impact: Productivity Enhancement, Task Optimization, Daily Utility."""
        description = idea.get("description", "").lower()
        
        productivity_score = 0.9 if "productivity" in description or "efficiency" in description else 0.6
        optimization_score = 0.8 if "automate" in description or "optimize" in description else 0.6
        utility_score = 0.8 if "daily" in description or "regular" in description else 0.7
        
        score = (productivity_score + optimization_score + utility_score) / 3
        
        return {
            "score": score,
            "productivity_enhancement": productivity_score,
            "task_optimization": optimization_score,
            "daily_utility": utility_score,
            "feedback": "Strong business impact potential" if score > 0.7 else "Business impact needs strengthening"
        }
    
    def _evaluate_market_validation(self, idea: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate market validation: Credibility, Uniqueness, Adaptability."""
        # Use market data if available
        has_market_data = bool(market_data.get("results", []))
        
        credibility_score = 0.9 if has_market_data else 0.6
        uniqueness_score = 0.8  # Default assumption, would need competitive analysis
        adaptability_score = 0.8 if "flexible" in idea.get("description", "").lower() else 0.7
        
        score = (credibility_score + uniqueness_score + adaptability_score) / 3
        
        return {
            "score": score,
            "credibility": credibility_score,
            "uniqueness": uniqueness_score,
            "adaptability": adaptability_score,
            "feedback": "Well-validated market opportunity" if score > 0.7 else "Requires additional market validation"
        }
    
    def _evaluate_quality_assurance(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate quality assurance: Non-theoretical, Employee-centric, Well-researched."""
        description = idea.get("description", "").lower()
        
        practical_score = 0.9 if "practical" in description or "real" in description else 0.7
        employee_score = 0.8 if "employee" in description or "user" in description else 0.6
        research_score = 0.8 if "research" in description or "data" in description else 0.6
        
        score = (practical_score + employee_score + research_score) / 3
        
        return {
            "score": score,
            "non_theoretical": practical_score,
            "employee_centric": employee_score,
            "well_researched": research_score,
            "feedback": "Meets quality standards" if score > 0.7 else "Quality aspects need improvement"
        }
    
    def _generate_suggestions(self, evaluation: Dict[str, Any]) -> List[str]:
        """Generate improvement suggestions based on evaluation scores."""
        suggestions = []
        
        if evaluation["practicality"]["score"] < 0.7:
            suggestions.append("Simplify the implementation approach and clarify the value proposition")
        
        if evaluation["business_impact"]["score"] < 0.7:
            suggestions.append("Emphasize productivity gains and daily utility for employees")
        
        if evaluation["market_validation"]["score"] < 0.7:
            suggestions.append("Conduct additional market research and competitive analysis")
        
        if evaluation["quality_assurance"]["score"] < 0.7:
            suggestions.append("Focus on practical applications and employee-centric benefits")
        
        if not suggestions:
            suggestions.append("Idea meets quality standards - consider minor refinements for optimization")
        
        return suggestions


class SentimentAnalysisTool(BaseTool):
    """Tool for analyzing sentiment in market research data."""
    
    def __init__(self):
        super().__init__(
            name="sentiment_analysis",
            description="Analyze sentiment in market research and feedback data",
            parameters={
                "text_data": {
                    "type": "array",
                    "description": "Array of text snippets to analyze"
                }
            }
        )
    
    def execute(self, text_data: List[str]) -> Dict[str, Any]:
        """
        Analyze sentiment in text data.
        
        Args:
            text_data: List of text snippets to analyze
            
        Returns:
            Dict containing sentiment analysis results
        """
        try:
            # Simple sentiment analysis (in production, use proper NLP)
            positive_words = ["good", "great", "excellent", "positive", "opportunity", "growth"]
            negative_words = ["bad", "poor", "negative", "problem", "issue", "decline"]
            
            sentiments = []
            for text in text_data:
                text_lower = text.lower()
                pos_count = sum(1 for word in positive_words if word in text_lower)
                neg_count = sum(1 for word in negative_words if word in text_lower)
                
                if pos_count > neg_count:
                    sentiment = "positive"
                elif neg_count > pos_count:
                    sentiment = "negative"
                else:
                    sentiment = "neutral"
                
                sentiments.append({
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "sentiment": sentiment,
                    "confidence": abs(pos_count - neg_count) / max(len(text.split()), 1)
                })
            
            # Overall sentiment
            sentiment_counts = {"positive": 0, "negative": 0, "neutral": 0}
            for s in sentiments:
                sentiment_counts[s["sentiment"]] += 1
            
            overall = max(sentiment_counts, key=sentiment_counts.get)
            
            return {
                "overall_sentiment": overall,
                "sentiment_distribution": sentiment_counts,
                "individual_sentiments": sentiments,
                "total_analyzed": len(text_data)
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")
            return {"error": str(e), "overall_sentiment": "neutral"}


# Tool instances for export
idea_evaluation_tool = IdeaEvaluationTool()
sentiment_analysis_tool = SentimentAnalysisTool()

# Placeholder tools (to be implemented)
keyword_extraction_tool = None
topic_modeling_tool = None
summarization_tool = None
insight_generation_tool = None
