"""
Research tools for the Multi-Agent Idea Generation System.

These tools provide internet research capabilities using Google Search API
as specified in the PRD requirements.
"""

import json
import requests
from typing import List, Dict, Any, Optional
from google.adk.tools import BaseTool
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class WebSearchTool(BaseTool):
    """
    Google Search tool for market research and validation data.
    Implements the Internet Tool requirement from the PRD.
    """
    
    def __init__(self, config: Config):
        self.config = config
        super().__init__(
            name="web_search",
            description="Search the internet for market trends, business opportunities, and validation data",
            parameters={
                "query": {
                    "type": "string",
                    "description": "Search query for market research"
                },
                "num_results": {
                    "type": "integer", 
                    "description": "Number of search results to return (default: 10)",
                    "default": 10
                }
            }
        )
    
    def execute(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        Execute Google Search for the given query.
        
        Args:
            query: Search query string
            num_results: Number of results to return
            
        Returns:
            Dict containing search results and metadata
        """
        try:
            if not self.config.google_search_api_key or not self.config.google_search_engine_id:
                logger.warning("Google Search API not configured, using mock data")
                return self._mock_search_results(query, num_results)
            
            # Google Custom Search API endpoint
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.config.google_search_api_key,
                "cx": self.config.google_search_engine_id,
                "q": query,
                "num": min(num_results, 10)  # API limit is 10 per request
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract relevant information
            results = []
            for item in data.get("items", []):
                results.append({
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", ""),
                    "displayLink": item.get("displayLink", "")
                })
            
            return {
                "query": query,
                "total_results": data.get("searchInformation", {}).get("totalResults", "0"),
                "search_time": data.get("searchInformation", {}).get("searchTime", "0"),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Search failed for query '{query}': {str(e)}")
            return self._mock_search_results(query, num_results)
    
    def _mock_search_results(self, query: str, num_results: int) -> Dict[str, Any]:
        """Generate mock search results for testing when API is not available."""
        return {
            "query": query,
            "total_results": "1000000",
            "search_time": "0.45",
            "results": [
                {
                    "title": f"Market Research: {query}",
                    "link": "https://example.com/research",
                    "snippet": f"Comprehensive analysis of {query} market trends and opportunities...",
                    "displayLink": "example.com"
                },
                {
                    "title": f"Business Opportunities in {query}",
                    "link": "https://example.com/opportunities", 
                    "snippet": f"Emerging trends and gaps in the {query} industry...",
                    "displayLink": "example.com"
                }
            ][:num_results]
        }


class TrendAnalysisTool(BaseTool):
    """
    Tool for analyzing market trends from search results.
    """
    
    def __init__(self):
        super().__init__(
            name="trend_analysis",
            description="Analyze market trends and identify opportunities from search data",
            parameters={
                "search_results": {
                    "type": "object",
                    "description": "Search results from web search tool"
                },
                "focus_area": {
                    "type": "string",
                    "description": "Specific focus area or industry to analyze"
                }
            }
        )
    
    def execute(self, search_results: Dict[str, Any], focus_area: str) -> Dict[str, Any]:
        """
        Analyze trends from search results.
        
        Args:
            search_results: Results from web search
            focus_area: Industry or domain focus
            
        Returns:
            Dict containing trend analysis
        """
        try:
            results = search_results.get("results", [])
            
            # Extract key themes and trends
            themes = []
            opportunities = []
            
            for result in results:
                title = result.get("title", "")
                snippet = result.get("snippet", "")
                
                # Simple keyword extraction (in real implementation, use NLP)
                text = f"{title} {snippet}".lower()
                
                # Identify trend indicators
                if any(word in text for word in ["trend", "growing", "emerging", "increasing"]):
                    themes.append({
                        "source": result.get("displayLink", ""),
                        "trend": title,
                        "description": snippet
                    })
                
                # Identify opportunities
                if any(word in text for word in ["opportunity", "gap", "need", "demand"]):
                    opportunities.append({
                        "source": result.get("displayLink", ""),
                        "opportunity": title,
                        "description": snippet
                    })
            
            return {
                "focus_area": focus_area,
                "trends_identified": len(themes),
                "opportunities_found": len(opportunities),
                "key_themes": themes[:5],  # Top 5 themes
                "market_opportunities": opportunities[:5],  # Top 5 opportunities
                "analysis_summary": f"Found {len(themes)} trends and {len(opportunities)} opportunities in {focus_area}"
            }
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {str(e)}")
            return {
                "focus_area": focus_area,
                "error": str(e),
                "trends_identified": 0,
                "opportunities_found": 0
            }


# Tool instances for export
def create_web_search_tool(config: Config) -> WebSearchTool:
    """Create and return a configured web search tool."""
    return WebSearchTool(config)


def create_trend_analysis_tool() -> TrendAnalysisTool:
    """Create and return a trend analysis tool."""
    return TrendAnalysisTool()


# Convenience functions for backward compatibility
web_search_tool = None  # Will be initialized with config
trend_analysis_tool = create_trend_analysis_tool()
content_extractor_tool = None  # To be implemented
competitor_analysis_tool = None  # To be implemented
