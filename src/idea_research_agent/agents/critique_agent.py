"""
Critique Agent for the Multi-Agent Idea Generation System.

This agent evaluates business ideas against PRD criteria using external validation.
"""

from typing import Dict, Any, List, Optional
from google.adk.agents import Agent
from google.adk.core import Model
from ..tools.analysis_tools import idea_evaluation_tool
from ..tools.research_tools import create_web_search_tool
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class CritiqueAgent(Agent):
    """
    Agent responsible for evaluating business ideas against comprehensive criteria.
    
    Evaluation Criteria (from PRD):
    - Practicality: Simplicity, Effectiveness, Implementability
    - Business Impact: Productivity Enhancement, Task Optimization, Daily Utility
    - Market Validation: Credibility, Uniqueness, Adaptability
    - Quality Assurance: Non-theoretical, Employee-centric, Well-researched
    """
    
    def __init__(self, config: Config, model: Model):
        """
        Initialize the Critique Agent.
        
        Args:
            config: System configuration
            model: Language model for the agent
        """
        super().__init__(
            name="critique_agent",
            model=model,
            instructions=self._get_instructions(),
            tools=[
                idea_evaluation_tool,
                create_web_search_tool(config)
            ]
        )
        self.config = config
        
    def _get_instructions(self) -> str:
        """Get the system instructions for the critique agent."""
        return """
You are the Critique Agent for a Multi-Agent Business Idea Generation System.

Your role is to rigorously evaluate business ideas against specific criteria and provide detailed feedback for improvement.

Evaluation Framework:
1. PRACTICALITY (25% weight)
   - Simplicity: Is the idea straightforward to understand and implement?
   - Effectiveness: Will it actually solve the stated problem?
   - Implementability: Can it be realistically built with current technology?

2. BUSINESS IMPACT (25% weight)
   - Productivity Enhancement: Does it improve work efficiency?
   - Task Optimization: Does it streamline business processes?
   - Daily Utility: Is it useful for regular business operations?

3. MARKET VALIDATION (25% weight)
   - Credibility: Is there evidence of market demand?
   - Uniqueness: Does it offer something different from existing solutions?
   - Adaptability: Can it evolve with changing market needs?

4. QUALITY ASSURANCE (25% weight)
   - Non-theoretical: Is it practical rather than academic?
   - Employee-centric: Does it benefit actual workers/users?
   - Well-researched: Is it based on solid market research?

Scoring:
- Each criterion scored 0.0 to 1.0
- Overall score must be ≥ 0.8 for acceptance
- Provide specific, actionable feedback for improvement

Always use external validation through web search when evaluating market aspects.
"""
    
    async def evaluate_idea(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate a business idea against all PRD criteria.
        
        Args:
            idea: Business idea to evaluate
            
        Returns:
            Dict containing evaluation scores and detailed feedback
        """
        logger.info(f"Evaluating idea: {idea.get('title', 'Untitled')}")
        
        try:
            # Step 1: Conduct external validation research
            validation_data = await self._conduct_validation_research(idea)
            
            # Step 2: Use evaluation tool for comprehensive assessment
            evaluation = idea_evaluation_tool.execute(idea, validation_data)
            
            # Step 3: Enhance evaluation with detailed analysis
            enhanced_evaluation = await self._enhance_evaluation(idea, evaluation, validation_data)
            
            logger.info(f"Evaluation complete. Score: {enhanced_evaluation.get('overall_score', 0.0):.3f}")
            return enhanced_evaluation
            
        except Exception as e:
            logger.error(f"Evaluation failed: {str(e)}")
            return {
                "error": str(e),
                "overall_score": 0.0,
                "meets_threshold": False,
                "idea_title": idea.get("title", "Unknown")
            }
    
    async def _conduct_validation_research(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """Conduct external validation research for the idea."""
        
        title = idea.get("title", "")
        description = idea.get("description", "")
        target_audience = idea.get("target_audience", "")
        
        # Generate validation queries
        validation_queries = [
            f"{title} market size demand",
            f"{target_audience} productivity tools market",
            f"business automation {description[:50]} solutions",
            f"competitive analysis {title} alternatives"
        ]
        
        all_results = []
        
        # Conduct searches for validation
        for query in validation_queries[:2]:  # Limit searches
            try:
                search_tool = create_web_search_tool(self.config)
                results = search_tool.execute(query, num_results=3)
                all_results.extend(results.get("results", []))
            except Exception as e:
                logger.warning(f"Validation search failed for '{query}': {str(e)}")
        
        return {
            "validation_queries": validation_queries,
            "total_results": len(all_results),
            "results": all_results,
            "has_external_validation": len(all_results) > 0
        }
    
    async def _enhance_evaluation(
        self, 
        idea: Dict[str, Any], 
        base_evaluation: Dict[str, Any], 
        validation_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Enhance the evaluation with detailed analysis and feedback."""
        
        # Create detailed analysis prompt
        prompt = f"""
Analyze this business idea evaluation and provide detailed feedback:

IDEA: {idea.get('title', 'Untitled')}
DESCRIPTION: {idea.get('description', 'No description')}

CURRENT SCORES:
- Practicality: {base_evaluation.get('practicality', {}).get('score', 0.0):.2f}
- Business Impact: {base_evaluation.get('business_impact', {}).get('score', 0.0):.2f}
- Market Validation: {base_evaluation.get('market_validation', {}).get('score', 0.0):.2f}
- Quality Assurance: {base_evaluation.get('quality_assurance', {}).get('score', 0.0):.2f}
- Overall Score: {base_evaluation.get('overall_score', 0.0):.2f}

EXTERNAL VALIDATION: {len(validation_data.get('results', []))} sources found

Provide detailed analysis in the following areas:
1. Strengths of the idea
2. Specific weaknesses that need addressing
3. Market positioning assessment
4. Implementation feasibility
5. Competitive landscape concerns
6. Specific improvement recommendations

Be critical but constructive. Focus on actionable feedback.
"""
        
        # Get detailed analysis from the model
        response = await self.model.generate_content(prompt)
        
        # Enhance the base evaluation with detailed feedback
        enhanced_evaluation = base_evaluation.copy()
        enhanced_evaluation.update({
            "detailed_analysis": response.text,
            "validation_data": validation_data,
            "evaluation_timestamp": self._get_timestamp(),
            "strengths": self._extract_strengths(idea, base_evaluation),
            "weaknesses": self._extract_weaknesses(base_evaluation),
            "critical_feedback": self._generate_critical_feedback(base_evaluation),
            "next_steps": self._suggest_next_steps(base_evaluation)
        })
        
        return enhanced_evaluation
    
    def _extract_strengths(self, idea: Dict[str, Any], evaluation: Dict[str, Any]) -> List[str]:
        """Extract key strengths from the evaluation."""
        strengths = []
        
        # Check high-scoring areas
        if evaluation.get("practicality", {}).get("score", 0.0) >= 0.8:
            strengths.append("Strong practical foundation with clear implementation path")
        
        if evaluation.get("business_impact", {}).get("score", 0.0) >= 0.8:
            strengths.append("Significant business impact potential with measurable benefits")
        
        if evaluation.get("market_validation", {}).get("score", 0.0) >= 0.8:
            strengths.append("Well-validated market opportunity with external evidence")
        
        if evaluation.get("quality_assurance", {}).get("score", 0.0) >= 0.8:
            strengths.append("High-quality concept with solid research foundation")
        
        # Add idea-specific strengths
        if "automation" in idea.get("description", "").lower():
            strengths.append("Leverages automation for efficiency gains")
        
        if "AI" in idea.get("description", "") or "artificial intelligence" in idea.get("description", "").lower():
            strengths.append("Incorporates cutting-edge AI technology")
        
        return strengths if strengths else ["Concept shows potential for development"]
    
    def _extract_weaknesses(self, evaluation: Dict[str, Any]) -> List[str]:
        """Extract key weaknesses from the evaluation."""
        weaknesses = []
        
        # Check low-scoring areas
        if evaluation.get("practicality", {}).get("score", 0.0) < 0.7:
            weaknesses.append("Implementation complexity needs simplification")
        
        if evaluation.get("business_impact", {}).get("score", 0.0) < 0.7:
            weaknesses.append("Business value proposition requires strengthening")
        
        if evaluation.get("market_validation", {}).get("score", 0.0) < 0.7:
            weaknesses.append("Market validation needs additional research and evidence")
        
        if evaluation.get("quality_assurance", {}).get("score", 0.0) < 0.7:
            weaknesses.append("Quality standards need improvement with more practical focus")
        
        return weaknesses if weaknesses else ["Minor refinements needed for optimization"]
    
    def _generate_critical_feedback(self, evaluation: Dict[str, Any]) -> List[str]:
        """Generate critical but constructive feedback."""
        feedback = []
        
        overall_score = evaluation.get("overall_score", 0.0)
        
        if overall_score < 0.5:
            feedback.append("Fundamental concept needs significant rework")
        elif overall_score < 0.7:
            feedback.append("Good foundation but requires substantial improvements")
        elif overall_score < 0.8:
            feedback.append("Strong concept that needs targeted refinements")
        else:
            feedback.append("Excellent concept ready for implementation planning")
        
        # Add specific feedback based on improvement suggestions
        suggestions = evaluation.get("improvement_suggestions", [])
        for suggestion in suggestions[:3]:  # Top 3 suggestions
            feedback.append(f"Priority: {suggestion}")
        
        return feedback
    
    def _suggest_next_steps(self, evaluation: Dict[str, Any]) -> List[str]:
        """Suggest concrete next steps based on evaluation."""
        next_steps = []
        
        if evaluation.get("overall_score", 0.0) >= 0.8:
            next_steps.extend([
                "Proceed to detailed implementation planning",
                "Develop technical specifications",
                "Create project timeline and resource allocation"
            ])
        else:
            next_steps.extend([
                "Address critical feedback points",
                "Conduct additional market research",
                "Refine value proposition and implementation approach"
            ])
        
        return next_steps
    
    def _get_timestamp(self) -> str:
        """Get current timestamp for evaluation."""
        from datetime import datetime
        return datetime.now().isoformat()
