"""
Orchestrator Agent for the Multi-Agent Idea Generation System.

This agent coordinates the entire idea generation, critique, and refinement process
as specified in the PRD requirements.
"""

from typing import Dict, Any, List, Optional
from google.adk.agents import Agent
from google.adk.core import Model
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class CoordinatorAgent(Agent):
    """
    Central orchestrator that manages the multi-agent workflow.
    
    Responsibilities:
    - Coordinate agent interactions
    - Manage iteration cycles
    - Ensure convergence criteria are met
    - Prevent infinite loops
    """
    
    def __init__(self, config: Config, model: Model):
        """
        Initialize the Coordinator Agent.
        
        Args:
            config: System configuration
            model: Language model for the agent
        """
        super().__init__(
            name="coordinator",
            model=model,
            instructions=self._get_instructions()
        )
        self.config = config
        self.max_iterations = config.max_iterations
        self.quality_threshold = config.quality_threshold
        
    def _get_instructions(self) -> str:
        """Get the system instructions for the coordinator agent."""
        return """
You are the Orchestrator Agent for a Multi-Agent Idea Generation System.

Your responsibilities:
1. Coordinate the workflow between Idea Generator, Critique, Refinement, and Output Formatter agents
2. Manage iteration cycles to ensure quality improvement
3. Monitor convergence criteria and prevent infinite loops
4. Make decisions about when to proceed to the next stage
5. Ensure the final output meets the quality threshold of 0.8

Workflow Process:
1. Receive user input (topic/domain)
2. Delegate to Idea Generator Agent for initial concept
3. Send idea to Critique Agent for evaluation
4. If score < 0.8, send to Refinement Agent with feedback
5. Repeat steps 3-4 until convergence or max iterations
6. Send final idea to Output Formatter Agent

Always maintain clear communication between agents and track progress toward quality goals.
"""
    
    async def coordinate_idea_generation(
        self, 
        topic: str, 
        idea_generator: 'IdeaGeneratorAgent',
        critique_agent: 'CritiqueAgent', 
        refinement_agent: 'RefinementAgent',
        formatter_agent: 'OutputFormatterAgent'
    ) -> Dict[str, Any]:
        """
        Coordinate the complete idea generation workflow.
        
        Args:
            topic: The business domain or topic to generate ideas for
            idea_generator: Agent for generating initial ideas
            critique_agent: Agent for evaluating ideas
            refinement_agent: Agent for improving ideas
            formatter_agent: Agent for formatting final output
            
        Returns:
            Dict containing the final refined idea and process metadata
        """
        logger.info(f"Starting idea generation workflow for topic: {topic}")
        
        workflow_state = {
            "topic": topic,
            "current_iteration": 0,
            "converged": False,
            "final_score": 0.0,
            "iteration_history": []
        }
        
        try:
            # Step 1: Generate initial idea
            logger.info("Step 1: Generating initial business idea")
            current_idea = await idea_generator.generate_idea(topic)
            
            # Iteration loop for refinement
            while (workflow_state["current_iteration"] < self.max_iterations and 
                   not workflow_state["converged"]):
                
                iteration_num = workflow_state["current_iteration"] + 1
                logger.info(f"Starting iteration {iteration_num}")
                
                # Step 2: Critique the current idea
                logger.info(f"Step 2: Evaluating idea (iteration {iteration_num})")
                evaluation = await critique_agent.evaluate_idea(current_idea)
                
                current_score = evaluation.get("overall_score", 0.0)
                workflow_state["final_score"] = current_score
                
                # Record iteration
                workflow_state["iteration_history"].append({
                    "iteration": iteration_num,
                    "score": current_score,
                    "idea_title": current_idea.get("title", "Untitled"),
                    "meets_threshold": current_score >= self.quality_threshold
                })
                
                # Check convergence
                if current_score >= self.quality_threshold:
                    logger.info(f"Convergence achieved! Score: {current_score:.3f}")
                    workflow_state["converged"] = True
                    break
                
                # Step 3: Refine the idea if not converged
                if iteration_num < self.max_iterations:
                    logger.info(f"Step 3: Refining idea (score: {current_score:.3f})")
                    current_idea = await refinement_agent.refine_idea(
                        current_idea, 
                        evaluation
                    )
                
                workflow_state["current_iteration"] = iteration_num
            
            # Step 4: Format final output
            logger.info("Step 4: Formatting final output")
            final_output = await formatter_agent.format_output(
                current_idea, 
                evaluation,
                workflow_state
            )
            
            # Compile final results
            result = {
                "success": True,
                "final_idea": current_idea,
                "final_evaluation": evaluation,
                "formatted_output": final_output,
                "workflow_metadata": {
                    "total_iterations": workflow_state["current_iteration"],
                    "converged": workflow_state["converged"],
                    "final_score": workflow_state["final_score"],
                    "quality_threshold": self.quality_threshold,
                    "iteration_history": workflow_state["iteration_history"]
                }
            }
            
            logger.info(f"Workflow completed successfully. Final score: {workflow_state['final_score']:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"Workflow failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "workflow_metadata": workflow_state
            }
    
    def should_continue_iteration(self, current_score: float, iteration: int) -> bool:
        """
        Determine if the iteration should continue based on score and limits.
        
        Args:
            current_score: Current evaluation score
            iteration: Current iteration number
            
        Returns:
            bool: True if iteration should continue
        """
        if current_score >= self.quality_threshold:
            return False
        
        if iteration >= self.max_iterations:
            return False
        
        return True
    
    def get_workflow_summary(self, workflow_metadata: Dict[str, Any]) -> str:
        """
        Generate a summary of the workflow process.
        
        Args:
            workflow_metadata: Metadata from the workflow execution
            
        Returns:
            str: Human-readable workflow summary
        """
        total_iterations = workflow_metadata.get("total_iterations", 0)
        converged = workflow_metadata.get("converged", False)
        final_score = workflow_metadata.get("final_score", 0.0)
        
        summary = f"""
Workflow Summary:
- Total Iterations: {total_iterations}
- Converged: {'Yes' if converged else 'No'}
- Final Score: {final_score:.3f}
- Quality Threshold: {self.quality_threshold}
- Status: {'Success' if converged else 'Max iterations reached'}
"""
        
        return summary.strip()
