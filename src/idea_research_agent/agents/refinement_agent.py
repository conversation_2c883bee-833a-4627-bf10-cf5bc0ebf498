"""
Refinement Agent for the Multi-Agent Idea Generation System.

This agent improves business ideas based on critique feedback to meet quality standards.
"""

import json
from typing import Dict, Any, List, Optional
from google.adk.agents import Agent
from google.adk.core import Model
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class RefinementAgent(Agent):
    """
    Agent responsible for refining and improving business ideas based on critique feedback.
    
    Capabilities:
    - Analyze critique feedback and identify improvement areas
    - Refine idea components to address weaknesses
    - Enhance value propositions and implementation approaches
    - Ensure ideas meet quality threshold requirements
    """
    
    def __init__(self, config: Config, model: Model):
        """
        Initialize the Refinement Agent.
        
        Args:
            config: System configuration
            model: Language model for the agent
        """
        super().__init__(
            name="refinement_agent",
            model=model,
            instructions=self._get_instructions()
        )
        self.config = config
        self.quality_threshold = config.quality_threshold
        
    def _get_instructions(self) -> str:
        """Get the system instructions for the refinement agent."""
        return """
You are the Refinement Agent for a Multi-Agent Business Idea Generation System.

Your role is to improve business ideas based on critique feedback to meet quality standards.

Refinement Process:
1. Analyze critique feedback and identify specific improvement areas
2. Address weaknesses while preserving strengths
3. Enhance value propositions and practical implementation details
4. Ensure the refined idea meets the 0.8 quality threshold

Focus Areas for Refinement:
- PRACTICALITY: Simplify complex aspects, clarify implementation steps
- BUSINESS IMPACT: Strengthen value proposition, quantify benefits
- MARKET VALIDATION: Add market evidence, competitive differentiation
- QUALITY ASSURANCE: Make more practical, employee-focused, research-backed

Refinement Strategies:
- Simplify overly complex concepts
- Add specific, measurable benefits
- Include implementation timelines and resource requirements
- Strengthen market positioning and competitive advantages
- Add concrete use cases and success metrics
- Improve target audience definition

Output Requirements:
- Maintain the original idea structure and JSON format
- Preserve successful elements while improving weak areas
- Add new details that address specific critique points
- Ensure all refinements are practical and implementable

Always explain what changes were made and why.
"""
    
    async def refine_idea(self, idea: Dict[str, Any], evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Refine a business idea based on critique evaluation.
        
        Args:
            idea: Original business idea to refine
            evaluation: Critique evaluation with scores and feedback
            
        Returns:
            Dict containing the refined business idea
        """
        logger.info(f"Refining idea: {idea.get('title', 'Untitled')}")
        
        try:
            # Step 1: Analyze the evaluation feedback
            refinement_plan = self._create_refinement_plan(idea, evaluation)
            
            # Step 2: Apply refinements based on the plan
            refined_idea = await self._apply_refinements(idea, evaluation, refinement_plan)
            
            # Step 3: Validate and enhance the refined idea
            final_idea = self._validate_refinements(refined_idea, idea)
            
            logger.info(f"Refinement complete for: {final_idea.get('title', 'Untitled')}")
            return final_idea
            
        except Exception as e:
            logger.error(f"Refinement failed: {str(e)}")
            # Return original idea with error note if refinement fails
            idea_copy = idea.copy()
            idea_copy["refinement_error"] = str(e)
            return idea_copy
    
    def _create_refinement_plan(self, idea: Dict[str, Any], evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """Create a plan for refining the idea based on evaluation feedback."""
        
        plan = {
            "priority_areas": [],
            "specific_improvements": [],
            "score_targets": {}
        }
        
        # Identify priority areas based on low scores
        scores = {
            "practicality": evaluation.get("practicality", {}).get("score", 0.0),
            "business_impact": evaluation.get("business_impact", {}).get("score", 0.0),
            "market_validation": evaluation.get("market_validation", {}).get("score", 0.0),
            "quality_assurance": evaluation.get("quality_assurance", {}).get("score", 0.0)
        }
        
        # Sort areas by score (lowest first)
        sorted_areas = sorted(scores.items(), key=lambda x: x[1])
        
        for area, score in sorted_areas:
            if score < self.quality_threshold:
                plan["priority_areas"].append(area)
                plan["score_targets"][area] = min(score + 0.2, 1.0)  # Target improvement
        
        # Add specific improvements from evaluation
        suggestions = evaluation.get("improvement_suggestions", [])
        plan["specific_improvements"] = suggestions
        
        # Add weaknesses to address
        weaknesses = evaluation.get("weaknesses", [])
        plan["weaknesses_to_address"] = weaknesses
        
        return plan
    
    async def _apply_refinements(
        self, 
        idea: Dict[str, Any], 
        evaluation: Dict[str, Any], 
        refinement_plan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply refinements to the idea based on the plan."""
        
        # Create refinement prompt
        prompt = self._create_refinement_prompt(idea, evaluation, refinement_plan)
        
        # Get refined idea from the model
        response = await self.model.generate_content(prompt)
        
        try:
            # Extract refined idea from response
            refined_idea = self._extract_refined_idea(response.text, idea)
            
            # Add refinement metadata
            refined_idea["refinement_metadata"] = {
                "original_score": evaluation.get("overall_score", 0.0),
                "target_improvements": refinement_plan["priority_areas"],
                "improvements_applied": refinement_plan["specific_improvements"],
                "refinement_iteration": idea.get("refinement_iteration", 0) + 1
            }
            
            return refined_idea
            
        except Exception as e:
            logger.error(f"Failed to parse refined idea: {str(e)}")
            return self._apply_manual_refinements(idea, refinement_plan)
    
    def _create_refinement_prompt(
        self, 
        idea: Dict[str, Any], 
        evaluation: Dict[str, Any], 
        refinement_plan: Dict[str, Any]
    ) -> str:
        """Create a detailed prompt for idea refinement."""
        
        prompt = f"""
Refine this business idea based on the critique evaluation and improvement plan:

ORIGINAL IDEA:
Title: {idea.get('title', 'Untitled')}
Description: {idea.get('description', 'No description')}

CURRENT EVALUATION SCORES:
- Practicality: {evaluation.get('practicality', {}).get('score', 0.0):.2f}
- Business Impact: {evaluation.get('business_impact', {}).get('score', 0.0):.2f}
- Market Validation: {evaluation.get('market_validation', {}).get('score', 0.0):.2f}
- Quality Assurance: {evaluation.get('quality_assurance', {}).get('score', 0.0):.2f}
- Overall Score: {evaluation.get('overall_score', 0.0):.2f}

PRIORITY IMPROVEMENT AREAS: {', '.join(refinement_plan['priority_areas'])}

SPECIFIC IMPROVEMENTS NEEDED:
{chr(10).join(f"- {imp}" for imp in refinement_plan['specific_improvements'])}

WEAKNESSES TO ADDRESS:
{chr(10).join(f"- {weak}" for weak in refinement_plan.get('weaknesses_to_address', []))}

REFINEMENT REQUIREMENTS:
1. Address each priority area with specific improvements
2. Maintain the original idea's core value while enhancing weak areas
3. Add concrete details, metrics, and implementation specifics
4. Ensure the refined idea is more practical and implementable
5. Target overall score improvement to ≥ 0.8

Provide the refined idea in the same JSON format as the original, with enhanced details:

{{
    "title": "Enhanced title if needed",
    "description": "Improved description addressing critique points",
    "problem_statement": "Clearer problem definition",
    "target_audience": "More specific target audience",
    "value_proposition": "Stronger value proposition with quantified benefits",
    "market_opportunity": "Enhanced market analysis with data",
    "implementation_approach": "Detailed implementation strategy",
    "success_metrics": "Specific, measurable success indicators",
    "competitive_advantages": "Clear differentiation points",
    "productivity_gains": "Quantified productivity improvements",
    "cost_savings": "Specific cost reduction estimates",
    "time_savings": "Measurable time efficiency gains",
    "expected_roi": "Detailed ROI analysis and timeline",
    "resource_requirements": "Specific resource and team requirements",
    "technical_dependencies": "Clear technical requirements",
    "scalability": "Detailed scalability analysis",
    "market_gap": "Specific market gap being addressed",
    "essential_datasets": "Detailed data requirements",
    "information_sources": "Specific information sources needed",
    "setup_requirements": "Step-by-step setup requirements"
}}

Focus on making the idea more practical, implementable, and valuable while addressing the specific critique points.
"""
        
        return prompt
    
    def _extract_refined_idea(self, response_text: str, original_idea: Dict[str, Any]) -> Dict[str, Any]:
        """Extract the refined idea from the model response."""
        
        try:
            # Find JSON in response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                refined_idea = json.loads(json_str)
                
                # Ensure all required fields are present
                for key, value in original_idea.items():
                    if key not in refined_idea and not key.startswith('refinement'):
                        refined_idea[key] = value
                
                return refined_idea
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            logger.error(f"JSON extraction failed: {str(e)}")
            raise
    
    def _apply_manual_refinements(self, idea: Dict[str, Any], refinement_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Apply basic refinements manually if model parsing fails."""
        
        refined_idea = idea.copy()
        
        # Enhance description with improvement focus
        if "practicality" in refinement_plan["priority_areas"]:
            refined_idea["description"] += " This solution emphasizes simplicity and practical implementation."
        
        if "business_impact" in refinement_plan["priority_areas"]:
            refined_idea["description"] += " Expected to deliver significant productivity gains and measurable ROI."
        
        if "market_validation" in refinement_plan["priority_areas"]:
            refined_idea["description"] += " Addresses validated market needs with strong competitive positioning."
        
        # Add basic improvements
        refined_idea["implementation_approach"] = refined_idea.get(
            "implementation_approach", 
            "Phased implementation with clear milestones and success metrics"
        )
        
        refined_idea["success_metrics"] = refined_idea.get(
            "success_metrics",
            "User adoption rate, productivity improvement percentage, cost savings achieved"
        )
        
        return refined_idea
    
    def _validate_refinements(self, refined_idea: Dict[str, Any], original_idea: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that refinements maintain idea integrity while improving quality."""
        
        # Ensure core fields are preserved
        core_fields = ["title", "description", "target_audience", "value_proposition"]
        
        for field in core_fields:
            if field not in refined_idea and field in original_idea:
                refined_idea[field] = original_idea[field]
        
        # Add refinement tracking
        refined_idea["refinement_history"] = original_idea.get("refinement_history", [])
        refined_idea["refinement_history"].append({
            "iteration": refined_idea.get("refinement_metadata", {}).get("refinement_iteration", 1),
            "improvements_applied": refined_idea.get("refinement_metadata", {}).get("improvements_applied", [])
        })
        
        return refined_idea
