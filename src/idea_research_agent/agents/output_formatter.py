"""
Output Formatter Agent for the Multi-Agent Idea Generation System.

This agent formats final business ideas into standardized documentation as specified in the PRD.
"""

from typing import Dict, Any, List, Optional
from google.adk.agents import Agent
from google.adk.core import Model
from ..tools.report_tools import markdown_report_tool, export_tool
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class OutputFormatterAgent(Agent):
    """
    Agent responsible for formatting finalized business ideas into standardized documentation.
    
    Capabilities:
    - Generate standardized markdown reports
    - Export reports to files
    - Create comprehensive documentation with all required sections
    - Ensure consistent formatting across all outputs
    """
    
    def __init__(self, config: Config, model: Model):
        """
        Initialize the Output Formatter Agent.
        
        Args:
            config: System configuration
            model: Language model for the agent
        """
        super().__init__(
            name="output_formatter",
            model=model,
            instructions=self._get_instructions(),
            tools=[
                markdown_report_tool,
                export_tool
            ]
        )
        self.config = config
        
    def _get_instructions(self) -> str:
        """Get the system instructions for the output formatter agent."""
        return """
You are the Output Formatter Agent for a Multi-Agent Business Idea Generation System.

Your role is to create standardized, professional documentation for finalized business ideas.

Documentation Requirements (from PRD):
1. USE CASE: Problem statement, target audience, application scenarios
2. IMPACT: Quantifiable benefits, ROI, success metrics
3. COMPLEXITY: Implementation difficulty, resource requirements, dependencies
4. UNIQUENESS: Competitive differentiation, market positioning
5. DATA REQUIRED: Essential datasets, information sources, setup requirements

Format Standards:
- Professional markdown documentation
- Clear section headers and structure
- Quantified benefits and metrics where possible
- Implementation timelines and resource estimates
- Executive summary for quick overview
- Detailed evaluation scores and feedback

Quality Standards:
- Comprehensive coverage of all required sections
- Professional tone and presentation
- Actionable information for stakeholders
- Clear next steps and recommendations
- Proper formatting and readability

Always ensure the final output is ready for business stakeholder review.
"""
    
    async def format_output(
        self, 
        idea: Dict[str, Any], 
        evaluation: Dict[str, Any], 
        workflow_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format the final business idea into standardized documentation.
        
        Args:
            idea: Finalized business idea
            evaluation: Final evaluation results
            workflow_metadata: Metadata from the workflow process
            
        Returns:
            Dict containing formatted output and export information
        """
        logger.info(f"Formatting output for: {idea.get('title', 'Untitled')}")
        
        try:
            # Step 1: Generate comprehensive markdown report
            report_result = await self._generate_comprehensive_report(idea, evaluation, workflow_metadata)
            
            # Step 2: Export the report to file if configured
            export_result = None
            if self.config.save_artifacts:
                export_result = await self._export_report(report_result)
            
            # Step 3: Create final output package
            final_output = self._create_output_package(
                idea, 
                evaluation, 
                workflow_metadata, 
                report_result, 
                export_result
            )
            
            logger.info("Output formatting completed successfully")
            return final_output
            
        except Exception as e:
            logger.error(f"Output formatting failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea_title": idea.get("title", "Unknown")
            }
    
    async def _generate_comprehensive_report(
        self, 
        idea: Dict[str, Any], 
        evaluation: Dict[str, Any], 
        workflow_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a comprehensive markdown report using the report tool."""
        
        # Enhance the idea with workflow information
        enhanced_idea = idea.copy()
        enhanced_idea.update({
            "workflow_summary": self._create_workflow_summary(workflow_metadata),
            "final_score": evaluation.get("overall_score", 0.0),
            "quality_threshold_met": evaluation.get("meets_threshold", False)
        })
        
        # Generate the report using the markdown tool
        report_result = markdown_report_tool.execute(
            idea=enhanced_idea,
            evaluation=evaluation,
            market_research=idea.get("research_data", {})
        )
        
        return report_result
    
    async def _export_report(self, report_result: Dict[str, Any]) -> Dict[str, Any]:
        """Export the generated report to a file."""
        
        if not report_result.get("markdown_content"):
            raise ValueError("No markdown content to export")
        
        export_result = export_tool.execute(
            content=report_result["markdown_content"],
            filename=report_result["filename"],
            output_dir=self.config.artifacts_dir
        )
        
        return export_result
    
    def _create_output_package(
        self,
        idea: Dict[str, Any],
        evaluation: Dict[str, Any], 
        workflow_metadata: Dict[str, Any],
        report_result: Dict[str, Any],
        export_result: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create the final output package with all information."""
        
        output_package = {
            "success": True,
            "idea_summary": {
                "title": idea.get("title", "Untitled"),
                "description": idea.get("description", "No description"),
                "target_audience": idea.get("target_audience", "Not specified"),
                "final_score": evaluation.get("overall_score", 0.0),
                "meets_quality_threshold": evaluation.get("meets_threshold", False)
            },
            "evaluation_summary": {
                "overall_score": evaluation.get("overall_score", 0.0),
                "practicality_score": evaluation.get("practicality", {}).get("score", 0.0),
                "business_impact_score": evaluation.get("business_impact", {}).get("score", 0.0),
                "market_validation_score": evaluation.get("market_validation", {}).get("score", 0.0),
                "quality_assurance_score": evaluation.get("quality_assurance", {}).get("score", 0.0),
                "key_strengths": evaluation.get("strengths", []),
                "improvement_areas": evaluation.get("weaknesses", [])
            },
            "workflow_summary": {
                "total_iterations": workflow_metadata.get("total_iterations", 0),
                "converged": workflow_metadata.get("converged", False),
                "quality_threshold": workflow_metadata.get("quality_threshold", 0.8),
                "iteration_history": workflow_metadata.get("iteration_history", [])
            },
            "documentation": {
                "markdown_content": report_result.get("markdown_content", ""),
                "filename": report_result.get("filename", ""),
                "word_count": report_result.get("word_count", 0),
                "sections_included": report_result.get("sections", [])
            }
        }
        
        # Add export information if available
        if export_result:
            output_package["export_info"] = {
                "exported": export_result.get("success", False),
                "file_path": export_result.get("file_path", ""),
                "file_size_bytes": export_result.get("file_size_bytes", 0),
                "exported_at": export_result.get("exported_at", "")
            }
        
        # Add recommendations
        output_package["recommendations"] = self._generate_recommendations(
            idea, 
            evaluation, 
            workflow_metadata
        )
        
        return output_package
    
    def _create_workflow_summary(self, workflow_metadata: Dict[str, Any]) -> str:
        """Create a human-readable workflow summary."""
        
        total_iterations = workflow_metadata.get("total_iterations", 0)
        converged = workflow_metadata.get("converged", False)
        final_score = workflow_metadata.get("final_score", 0.0)
        
        summary = f"""
This idea was developed through {total_iterations} iteration(s) of generation, critique, and refinement.
Final evaluation score: {final_score:.3f}
Convergence status: {'Achieved quality threshold' if converged else 'Maximum iterations reached'}
"""
        
        # Add iteration details
        iteration_history = workflow_metadata.get("iteration_history", [])
        if iteration_history:
            summary += "\n\nIteration Progress:\n"
            for iteration in iteration_history:
                summary += f"- Iteration {iteration['iteration']}: Score {iteration['score']:.3f}\n"
        
        return summary.strip()
    
    def _generate_recommendations(
        self, 
        idea: Dict[str, Any], 
        evaluation: Dict[str, Any], 
        workflow_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate recommendations based on the final results."""
        
        recommendations = {
            "implementation_readiness": "Not Ready",
            "next_steps": [],
            "risk_factors": [],
            "success_factors": []
        }
        
        final_score = evaluation.get("overall_score", 0.0)
        
        # Determine implementation readiness
        if final_score >= 0.8:
            recommendations["implementation_readiness"] = "Ready for Implementation"
            recommendations["next_steps"] = [
                "Develop detailed project plan and timeline",
                "Allocate resources and form implementation team",
                "Create technical specifications and requirements",
                "Begin prototype development or pilot program"
            ]
        elif final_score >= 0.6:
            recommendations["implementation_readiness"] = "Needs Further Development"
            recommendations["next_steps"] = [
                "Address remaining evaluation concerns",
                "Conduct additional market research",
                "Refine implementation approach",
                "Seek stakeholder feedback and validation"
            ]
        else:
            recommendations["implementation_readiness"] = "Requires Major Revision"
            recommendations["next_steps"] = [
                "Fundamental concept revision needed",
                "Conduct comprehensive market analysis",
                "Reconsider target audience and value proposition",
                "Explore alternative approaches to the problem"
            ]
        
        # Identify risk factors
        if evaluation.get("practicality", {}).get("score", 0.0) < 0.7:
            recommendations["risk_factors"].append("Implementation complexity may cause delays")
        
        if evaluation.get("market_validation", {}).get("score", 0.0) < 0.7:
            recommendations["risk_factors"].append("Market acceptance uncertainty")
        
        if workflow_metadata.get("total_iterations", 0) >= workflow_metadata.get("max_iterations", 5):
            recommendations["risk_factors"].append("Idea required maximum iterations to develop")
        
        # Identify success factors
        strengths = evaluation.get("strengths", [])
        if strengths:
            recommendations["success_factors"] = strengths[:3]  # Top 3 strengths
        
        return recommendations
