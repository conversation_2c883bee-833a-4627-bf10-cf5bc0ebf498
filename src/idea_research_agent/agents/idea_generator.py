"""
Idea Generator Agent for the Multi-Agent Idea Generation System.

This agent generates business concepts using internet research as specified in the PRD.
"""

import json
from typing import Dict, Any, List, Optional
from google.adk.agents import LlmAgent
from google.adk.tools import google_search
from ..utils.config import Config
from ..utils.logger import get_logger

logger = get_logger(__name__)


class IdeaGeneratorAgent(LlmAgent):
    """
    Agent responsible for generating business ideas using internet research.
    
    Capabilities:
    - Conduct market research using Google Search
    - Identify trends and opportunities
    - Generate creative business concepts
    - Ensure ideas are grounded in real market data
    """
    
    def __init__(self, config: Config):
        """
        Initialize the Idea Generator Agent.

        Args:
            config: System configuration
        """
        super().__init__(
            name="idea_generator",
            model=config.default_model,
            instruction=self._get_instructions(),
            tools=[google_search]
        )
        self.config = config
        
    def _get_instructions(self) -> str:
        """Get the system instructions for the idea generator agent."""
        return """
You are the Idea Generator Agent for a Multi-Agent Business Idea Generation System.

Your role is to generate innovative, practical business ideas based on internet research and market analysis.

Process:
1. Research the given topic/domain using web search
2. Analyze market trends and identify opportunities
3. Generate a comprehensive business idea that addresses real market needs
4. Ensure the idea is practical, implementable, and has clear business value

Requirements for generated ideas:
- Must be grounded in real market research data
- Should address genuine business problems or opportunities
- Must be practical and implementable
- Should have clear value proposition
- Must include specific target audience and use cases

Output Format:
Generate ideas as structured JSON with these fields:
- title: Clear, descriptive title
- description: Detailed description (100-200 words)
- problem_statement: What problem does this solve?
- target_audience: Who will use this?
- value_proposition: What value does it provide?
- market_opportunity: Size and scope of opportunity
- implementation_approach: High-level implementation strategy
- success_metrics: How to measure success
- competitive_advantages: What makes this unique?

Always use the available tools to research before generating ideas.
"""
    
    async def generate_idea(self, topic: str) -> Dict[str, Any]:
        """
        Generate a business idea for the given topic using market research.
        
        Args:
            topic: The business domain or topic to generate ideas for
            
        Returns:
            Dict containing the generated business idea
        """
        logger.info(f"Generating business idea for topic: {topic}")
        
        try:
            # Step 1: Conduct market research
            market_research = await self._conduct_market_research(topic)
            
            # Step 2: Analyze trends and opportunities
            trend_analysis = await self._analyze_trends(market_research, topic)
            
            # Step 3: Generate the business idea
            business_idea = await self._generate_business_concept(
                topic, 
                market_research, 
                trend_analysis
            )
            
            logger.info(f"Generated idea: {business_idea.get('title', 'Untitled')}")
            return business_idea
            
        except Exception as e:
            logger.error(f"Idea generation failed: {str(e)}")
            return self._generate_fallback_idea(topic)
    
    async def _conduct_market_research(self, topic: str) -> Dict[str, Any]:
        """Conduct comprehensive market research for the topic."""

        # Generate research queries
        research_queries = [
            f"{topic} market trends 2024",
            f"{topic} business opportunities",
            f"{topic} industry challenges problems"
        ]

        # Note: With ADK google_search, the actual search is handled by the model
        # We'll simulate research results for now
        mock_results = [
            {
                "title": f"Market Analysis: {topic} Industry Trends",
                "snippet": f"The {topic} industry is experiencing significant growth with emerging opportunities in automation and AI integration.",
                "link": "https://example.com/market-analysis",
                "displayLink": "example.com"
            },
            {
                "title": f"Business Opportunities in {topic}",
                "snippet": f"Key opportunities identified in {topic} include process optimization, cost reduction, and enhanced user experience.",
                "link": "https://example.com/opportunities",
                "displayLink": "example.com"
            }
        ]

        return {
            "topic": topic,
            "total_results": len(mock_results),
            "results": mock_results,
            "research_queries": research_queries
        }
    
    async def _analyze_trends(self, market_research: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """Analyze trends from market research data."""
        
        try:
            trend_analysis = trend_analysis_tool.execute(market_research, topic)
            return trend_analysis
        except Exception as e:
            logger.warning(f"Trend analysis failed: {str(e)}")
            return {
                "focus_area": topic,
                "trends_identified": 0,
                "opportunities_found": 0,
                "key_themes": [],
                "market_opportunities": []
            }
    
    async def _generate_business_concept(
        self, 
        topic: str, 
        market_research: Dict[str, Any], 
        trend_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a business concept based on research and analysis."""
        
        # Extract key insights from research
        opportunities = trend_analysis.get("market_opportunities", [])
        themes = trend_analysis.get("key_themes", [])
        
        # Create a prompt for idea generation
        research_summary = self._summarize_research(market_research, trend_analysis)
        
        prompt = f"""
Based on the following market research for {topic}:

{research_summary}

Generate an innovative business idea that:
1. Addresses a real market need identified in the research
2. Is practical and implementable
3. Has clear business value and ROI potential
4. Targets a specific audience
5. Leverages current technology trends

Provide the idea in the following JSON format:
{{
    "title": "Clear, descriptive title",
    "description": "Detailed description (100-200 words)",
    "problem_statement": "What specific problem does this solve?",
    "target_audience": "Who will use this solution?",
    "value_proposition": "What unique value does it provide?",
    "market_opportunity": "Market size and growth potential",
    "implementation_approach": "High-level technical approach",
    "success_metrics": "Key performance indicators",
    "competitive_advantages": "What makes this unique?",
    "productivity_gains": "Expected productivity improvements",
    "cost_savings": "Potential cost reductions",
    "time_savings": "Time efficiency gains",
    "expected_roi": "Return on investment timeline",
    "resource_requirements": "Implementation resources needed",
    "technical_dependencies": "Key technical requirements",
    "scalability": "How well does this scale?",
    "market_gap": "What gap does this fill?",
    "essential_datasets": "Data needed for implementation",
    "information_sources": "Key information sources",
    "setup_requirements": "Initial setup needs"
}}
"""
        
        # Use the model to generate the idea
        response = await self.model.generate_content(prompt)
        
        try:
            # Parse the JSON response
            idea_json = self._extract_json_from_response(response.text)
            
            # Add metadata
            idea_json["generated_from_topic"] = topic
            idea_json["research_data"] = {
                "market_research_results": len(market_research.get("results", [])),
                "trends_identified": trend_analysis.get("trends_identified", 0),
                "opportunities_found": trend_analysis.get("opportunities_found", 0)
            }
            
            return idea_json
            
        except Exception as e:
            logger.error(f"Failed to parse generated idea: {str(e)}")
            return self._generate_fallback_idea(topic)
    
    def _summarize_research(self, market_research: Dict[str, Any], trend_analysis: Dict[str, Any]) -> str:
        """Create a summary of research findings."""
        
        summary_parts = []
        
        # Market research summary
        results = market_research.get("results", [])
        if results:
            summary_parts.append(f"Market Research ({len(results)} sources):")
            for i, result in enumerate(results[:3], 1):
                summary_parts.append(f"{i}. {result.get('title', 'Unknown')}: {result.get('snippet', 'No description')}")
        
        # Trend analysis summary
        opportunities = trend_analysis.get("market_opportunities", [])
        if opportunities:
            summary_parts.append(f"\nKey Opportunities ({len(opportunities)} identified):")
            for i, opp in enumerate(opportunities[:3], 1):
                summary_parts.append(f"{i}. {opp.get('opportunity', 'Unknown opportunity')}")
        
        themes = trend_analysis.get("key_themes", [])
        if themes:
            summary_parts.append(f"\nEmerging Themes ({len(themes)} identified):")
            for i, theme in enumerate(themes[:3], 1):
                summary_parts.append(f"{i}. {theme.get('trend', 'Unknown trend')}")
        
        return "\n".join(summary_parts) if summary_parts else "Limited research data available."
    
    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """Extract JSON from model response."""
        
        # Try to find JSON in the response
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1
        
        if start_idx != -1 and end_idx > start_idx:
            json_str = response_text[start_idx:end_idx]
            return json.loads(json_str)
        else:
            raise ValueError("No valid JSON found in response")
    
    def _generate_fallback_idea(self, topic: str) -> Dict[str, Any]:
        """Generate a basic fallback idea when research fails."""
        
        return {
            "title": f"AI-Powered {topic.title()} Optimization Platform",
            "description": f"An intelligent platform that leverages AI to optimize {topic} processes, improve efficiency, and reduce operational costs through automation and data-driven insights.",
            "problem_statement": f"Current {topic} processes are often manual, time-consuming, and prone to inefficiencies.",
            "target_audience": f"Businesses and organizations working in the {topic} domain",
            "value_proposition": "Significant productivity gains through AI-powered automation and optimization",
            "market_opportunity": "Growing demand for AI solutions in business process optimization",
            "implementation_approach": "Cloud-based SaaS platform with AI/ML capabilities",
            "success_metrics": "User adoption rate, process efficiency improvements, cost savings",
            "competitive_advantages": "AI-first approach with domain-specific optimization",
            "generated_from_topic": topic,
            "research_data": {"note": "Fallback idea - limited research data available"}
        }
