"""
Configuration management for the Idea Research Agent.
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
from dotenv import load_dotenv


@dataclass
class Config:
    """Configuration class for the Idea Research Agent."""
    
    # Google AI Configuration
    google_api_key: Optional[str] = None
    google_project_id: Optional[str] = None
    
    # Search Configuration
    google_search_api_key: Optional[str] = None
    google_search_engine_id: Optional[str] = None
    
    # AI Provider Keys
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # Agent Configuration (PRD Requirements)
    default_model: str = "gemini-2.5-flash-preview-05-20"  # PRD mandated model
    max_search_results: int = 10
    research_depth: int = 3
    max_iterations: int = 5  # Prevent infinite loops
    quality_threshold: float = 0.8  # Convergence criteria
    
    # Logging and Debug
    log_level: str = "INFO"
    debug_mode: bool = False
    
    # Output Configuration
    output_format: str = "markdown"
    save_artifacts: bool = True
    artifacts_dir: str = "./artifacts"
    
    # Additional settings
    extra_settings: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization to create artifacts directory if needed."""
        if self.save_artifacts and not os.path.exists(self.artifacts_dir):
            os.makedirs(self.artifacts_dir, exist_ok=True)


def load_config(env_file: Optional[str] = None) -> Config:
    """
    Load configuration from environment variables and .env file.
    
    Args:
        env_file: Optional path to .env file. If None, looks for .env in current directory.
        
    Returns:
        Config: Configured Config object.
    """
    # Load environment variables from .env file
    if env_file:
        load_dotenv(env_file)
    else:
        load_dotenv()
    
    return Config(
        google_api_key=os.getenv("GOOGLE_API_KEY"),
        google_project_id=os.getenv("GOOGLE_PROJECT_ID"),
        google_search_api_key=os.getenv("GOOGLE_SEARCH_API_KEY"),
        google_search_engine_id=os.getenv("GOOGLE_SEARCH_ENGINE_ID"),
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        anthropic_api_key=os.getenv("ANTHROPIC_API_KEY"),
        default_model=os.getenv("DEFAULT_MODEL", "gemini-2.5-flash-preview-0520"),
        max_search_results=int(os.getenv("MAX_SEARCH_RESULTS", "10")),
        research_depth=int(os.getenv("RESEARCH_DEPTH", "3")),
        max_iterations=int(os.getenv("MAX_ITERATIONS", "5")),
        quality_threshold=float(os.getenv("QUALITY_THRESHOLD", "0.8")),
        log_level=os.getenv("LOG_LEVEL", "INFO"),
        debug_mode=os.getenv("DEBUG_MODE", "false").lower() == "true",
        output_format=os.getenv("OUTPUT_FORMAT", "markdown"),
        save_artifacts=os.getenv("SAVE_ARTIFACTS", "true").lower() == "true",
        artifacts_dir=os.getenv("ARTIFACTS_DIR", "./artifacts"),
    )
