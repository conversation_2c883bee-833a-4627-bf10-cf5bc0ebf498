"""
Text processing utilities for the Idea Research Agent.
"""

import re
import json
from typing import Dict, Any, <PERSON>, Optional, Tuple
from ..utils.logger import get_logger

logger = get_logger(__name__)


class TextProcessor:
    """
    Utility class for text processing and analysis in the idea generation system.
    """
    
    @staticmethod
    def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
        """
        Extract JSON object from text that may contain other content.
        
        Args:
            text: Text that may contain JSON
            
        Returns:
            Optional[Dict[str, Any]]: Extracted JSON object or None
        """
        try:
            # Try to find JSO<PERSON> in the text
            json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            matches = re.findall(json_pattern, text, re.DOTALL)
            
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
            
            # If no valid JSON found, try extracting from code blocks
            code_block_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
            code_matches = re.findall(code_block_pattern, text, re.DOTALL)
            
            for match in code_matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"JSON extraction failed: {str(e)}")
            return None
    
    @staticmethod
    def clean_markdown(text: str) -> str:
        """
        Clean markdown formatting from text.
        
        Args:
            text: Text with markdown formatting
            
        Returns:
            str: Clean text without markdown
        """
        if not text:
            return ""
        
        # Remove markdown formatting
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # Italic
        text = re.sub(r'`(.*?)`', r'\1', text)        # Code
        text = re.sub(r'#{1,6}\s*(.*)', r'\1', text)  # Headers
        text = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', text)  # Links
        text = re.sub(r'!\[.*?\]\(.*?\)', '', text)   # Images
        
        return text.strip()
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 200, suffix: str = "...") -> str:
        """
        Truncate text to specified length with suffix.
        
        Args:
            text: Text to truncate
            max_length: Maximum length
            suffix: Suffix to add if truncated
            
        Returns:
            str: Truncated text
        """
        if not text or len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def extract_bullet_points(text: str) -> List[str]:
        """
        Extract bullet points from text.
        
        Args:
            text: Text containing bullet points
            
        Returns:
            List[str]: List of bullet points
        """
        if not text:
            return []
        
        # Pattern for various bullet point formats
        bullet_patterns = [
            r'^\s*[-•*]\s+(.+)$',  # - • * bullets
            r'^\s*\d+\.\s+(.+)$',  # Numbered lists
            r'^\s*[a-zA-Z]\.\s+(.+)$',  # Letter lists
        ]
        
        bullet_points = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            for pattern in bullet_patterns:
                match = re.match(pattern, line, re.MULTILINE)
                if match:
                    bullet_points.append(match.group(1).strip())
                    break
        
        return bullet_points
    
    @staticmethod
    def format_list_as_markdown(items: List[str], ordered: bool = False) -> str:
        """
        Format a list of items as markdown.
        
        Args:
            items: List of items to format
            ordered: Whether to use ordered list (numbers) or unordered (bullets)
            
        Returns:
            str: Markdown formatted list
        """
        if not items:
            return ""
        
        formatted_items = []
        for i, item in enumerate(items, 1):
            if ordered:
                formatted_items.append(f"{i}. {item}")
            else:
                formatted_items.append(f"- {item}")
        
        return '\n'.join(formatted_items)
    
    @staticmethod
    def calculate_readability_score(text: str) -> float:
        """
        Calculate a simple readability score for text.
        
        Args:
            text: Text to analyze
            
        Returns:
            float: Readability score (0.0 to 1.0, higher is more readable)
        """
        if not text:
            return 0.0
        
        # Simple readability metrics
        sentences = len(re.findall(r'[.!?]+', text))
        words = len(text.split())
        characters = len(text.replace(' ', ''))
        
        if sentences == 0 or words == 0:
            return 0.0
        
        # Average sentence length (words per sentence)
        avg_sentence_length = words / sentences
        
        # Average word length (characters per word)
        avg_word_length = characters / words
        
        # Simple readability score (lower is better, so invert)
        # Penalize very long sentences and very long words
        sentence_penalty = min(avg_sentence_length / 20, 1.0)  # Normalize to 0-1
        word_penalty = min(avg_word_length / 10, 1.0)  # Normalize to 0-1
        
        readability = 1.0 - (sentence_penalty * 0.6 + word_penalty * 0.4)
        
        return max(0.0, min(1.0, readability))
    
    @staticmethod
    def extract_key_phrases(text: str, max_phrases: int = 5) -> List[str]:
        """
        Extract key phrases from text using simple pattern matching.
        
        Args:
            text: Text to analyze
            max_phrases: Maximum number of phrases to return
            
        Returns:
            List[str]: Key phrases
        """
        if not text:
            return []
        
        # Simple phrase extraction patterns
        phrase_patterns = [
            r'\b(?:AI|artificial intelligence|machine learning|automation)\b[^.!?]*',
            r'\b(?:productivity|efficiency|optimization|improvement)\b[^.!?]*',
            r'\b(?:business|market|customer|user)\b[^.!?]*',
            r'\b(?:solution|platform|system|tool)\b[^.!?]*',
        ]
        
        phrases = []
        text_lower = text.lower()
        
        for pattern in phrase_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                phrase = match.strip()
                if len(phrase) > 10 and phrase not in phrases:
                    phrases.append(phrase)
        
        return phrases[:max_phrases]
    
    @staticmethod
    def validate_text_quality(text: str) -> Dict[str, Any]:
        """
        Validate text quality and provide metrics.
        
        Args:
            text: Text to validate
            
        Returns:
            Dict[str, Any]: Quality metrics
        """
        if not text:
            return {
                "is_valid": False,
                "word_count": 0,
                "readability_score": 0.0,
                "issues": ["Text is empty"]
            }
        
        word_count = len(text.split())
        readability = TextProcessor.calculate_readability_score(text)
        issues = []
        
        # Check for common issues
        if word_count < 10:
            issues.append("Text is too short")
        
        if word_count > 1000:
            issues.append("Text may be too long")
        
        if readability < 0.3:
            issues.append("Text may be difficult to read")
        
        # Check for repeated words
        words = text.lower().split()
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        repeated_words = [word for word, freq in word_freq.items() if freq > word_count * 0.1 and len(word) > 3]
        if repeated_words:
            issues.append(f"Repeated words detected: {', '.join(repeated_words[:3])}")
        
        return {
            "is_valid": len(issues) == 0,
            "word_count": word_count,
            "readability_score": readability,
            "issues": issues,
            "repeated_words": repeated_words
        }
    
    @staticmethod
    def generate_summary(text: str, max_sentences: int = 3) -> str:
        """
        Generate a simple extractive summary of text.
        
        Args:
            text: Text to summarize
            max_sentences: Maximum number of sentences in summary
            
        Returns:
            str: Generated summary
        """
        if not text:
            return ""
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) <= max_sentences:
            return text
        
        # Simple scoring based on sentence position and length
        scored_sentences = []
        for i, sentence in enumerate(sentences):
            score = 0
            
            # Position score (first and last sentences are important)
            if i == 0:
                score += 2
            elif i == len(sentences) - 1:
                score += 1
            
            # Length score (prefer medium-length sentences)
            word_count = len(sentence.split())
            if 10 <= word_count <= 30:
                score += 1
            
            scored_sentences.append((sentence, score))
        
        # Sort by score and take top sentences
        scored_sentences.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in scored_sentences[:max_sentences]]
        
        # Maintain original order
        summary_sentences = []
        for sentence in sentences:
            if sentence in top_sentences:
                summary_sentences.append(sentence)
        
        return '. '.join(summary_sentences) + '.'
