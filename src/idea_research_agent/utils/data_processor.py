"""
Data processing utilities for the Idea Research Agent.
"""

import json
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DataProcessor:
    """
    Utility class for processing and transforming data in the idea generation system.
    """
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        Clean and normalize text data.
        
        Args:
            text: Raw text to clean
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
        
        # Basic text cleaning
        cleaned = text.strip()
        cleaned = ' '.join(cleaned.split())  # Normalize whitespace
        
        return cleaned
    
    @staticmethod
    def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
        """
        Extract keywords from text using simple frequency analysis.
        
        Args:
            text: Text to analyze
            max_keywords: Maximum number of keywords to return
            
        Returns:
            List[str]: Extracted keywords
        """
        if not text:
            return []
        
        # Simple keyword extraction (in production, use proper NLP)
        words = text.lower().split()
        
        # Filter out common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
            'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him',
            'her', 'us', 'them'
        }
        
        # Count word frequencies
        word_freq = {}
        for word in words:
            word = word.strip('.,!?;:"()[]{}')
            if len(word) > 2 and word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top keywords
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:max_keywords]]
    
    @staticmethod
    def merge_search_results(results_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Merge multiple search result sets into a single consolidated result.
        
        Args:
            results_list: List of search result dictionaries
            
        Returns:
            Dict[str, Any]: Merged search results
        """
        if not results_list:
            return {"results": [], "total_results": 0}
        
        merged_results = []
        total_results = 0
        queries = []
        
        for result_set in results_list:
            if isinstance(result_set, dict):
                merged_results.extend(result_set.get("results", []))
                total_results += int(result_set.get("total_results", 0))
                
                query = result_set.get("query", "")
                if query and query not in queries:
                    queries.append(query)
        
        return {
            "results": merged_results,
            "total_results": total_results,
            "queries": queries,
            "merged_from": len(results_list)
        }
    
    @staticmethod
    def calculate_score_improvement(
        original_scores: Dict[str, float], 
        new_scores: Dict[str, float]
    ) -> Dict[str, float]:
        """
        Calculate improvement between two sets of scores.
        
        Args:
            original_scores: Original evaluation scores
            new_scores: New evaluation scores
            
        Returns:
            Dict[str, float]: Score improvements
        """
        improvements = {}
        
        for key in new_scores:
            if key in original_scores:
                improvement = new_scores[key] - original_scores[key]
                improvements[key] = improvement
        
        return improvements
    
    @staticmethod
    def format_currency(amount: Union[int, float], currency: str = "USD") -> str:
        """
        Format currency amounts for display.
        
        Args:
            amount: Numeric amount
            currency: Currency code
            
        Returns:
            str: Formatted currency string
        """
        if currency == "USD":
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f} {currency}"
    
    @staticmethod
    def format_percentage(value: float, decimal_places: int = 1) -> str:
        """
        Format percentage values for display.
        
        Args:
            value: Percentage value (0.0 to 1.0)
            decimal_places: Number of decimal places
            
        Returns:
            str: Formatted percentage string
        """
        percentage = value * 100
        return f"{percentage:.{decimal_places}f}%"
    
    @staticmethod
    def validate_idea_structure(idea: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and normalize idea structure.
        
        Args:
            idea: Idea dictionary to validate
            
        Returns:
            Dict[str, Any]: Validation results
        """
        required_fields = [
            "title", "description", "target_audience", "value_proposition"
        ]
        
        optional_fields = [
            "problem_statement", "market_opportunity", "implementation_approach",
            "success_metrics", "competitive_advantages", "productivity_gains",
            "cost_savings", "time_savings", "expected_roi"
        ]
        
        validation_result = {
            "is_valid": True,
            "missing_required": [],
            "missing_optional": [],
            "field_count": len(idea),
            "completeness_score": 0.0
        }
        
        # Check required fields
        for field in required_fields:
            if field not in idea or not idea[field]:
                validation_result["missing_required"].append(field)
                validation_result["is_valid"] = False
        
        # Check optional fields
        for field in optional_fields:
            if field not in idea or not idea[field]:
                validation_result["missing_optional"].append(field)
        
        # Calculate completeness score
        total_fields = len(required_fields) + len(optional_fields)
        present_fields = total_fields - len(validation_result["missing_required"]) - len(validation_result["missing_optional"])
        validation_result["completeness_score"] = present_fields / total_fields
        
        return validation_result
    
    @staticmethod
    def create_summary_statistics(evaluations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create summary statistics from multiple evaluations.
        
        Args:
            evaluations: List of evaluation dictionaries
            
        Returns:
            Dict[str, Any]: Summary statistics
        """
        if not evaluations:
            return {"count": 0, "average_score": 0.0}
        
        scores = [eval_dict.get("overall_score", 0.0) for eval_dict in evaluations]
        
        stats = {
            "count": len(evaluations),
            "average_score": sum(scores) / len(scores),
            "min_score": min(scores),
            "max_score": max(scores),
            "passing_count": sum(1 for score in scores if score >= 0.8),
            "passing_rate": sum(1 for score in scores if score >= 0.8) / len(scores)
        }
        
        return stats
