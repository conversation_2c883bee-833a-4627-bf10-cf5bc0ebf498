"""
Main Idea Research Agent - Multi-Agent Idea Generation System.

This is the primary interface for the idea generation system that coordinates
all specialized agents according to the PRD requirements.
"""

from typing import Dict, Any, Optional
from google.adk.agents import LlmAgent
from google.adk.tools import google_search

from .agents.coordinator import CoordinatorAgent
from .agents.idea_generator import IdeaGeneratorAgent
from .agents.critique_agent import CritiqueAgent
from .agents.refinement_agent import RefinementAgent
from .agents.output_formatter import OutputFormatterAgent
from .utils.config import Config, load_config
from .utils.logger import get_logger, setup_logger

logger = get_logger(__name__)


class IdeaResearchAgent:
    """
    Main Multi-Agent Idea Generation System.
    
    This class orchestrates the complete workflow of idea generation, critique,
    refinement, and output formatting as specified in the PRD.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the Idea Research Agent system.
        
        Args:
            config: Optional configuration. If None, loads from environment.
        """
        # Load configuration
        self.config = config or load_config()
        
        # Setup logging
        setup_logger(
            level=self.config.log_level,
            log_file="idea_research_agent.log" if self.config.save_artifacts else None
        )
        
        # Initialize all specialized agents
        self.coordinator = CoordinatorAgent(self.config)
        self.idea_generator = IdeaGeneratorAgent(self.config)
        self.critique_agent = CritiqueAgent(self.config)
        self.refinement_agent = RefinementAgent(self.config)
        self.output_formatter = OutputFormatterAgent(self.config)
        
        logger.info("Idea Research Agent system initialized successfully")
    

    
    async def generate_business_idea(self, topic: str) -> Dict[str, Any]:
        """
        Generate a comprehensive business idea for the given topic.
        
        This is the main entry point that orchestrates the complete workflow:
        1. Idea Generation using internet research
        2. Critique and evaluation against PRD criteria
        3. Iterative refinement until quality threshold is met
        4. Professional output formatting and documentation
        
        Args:
            topic: The business domain or topic to generate ideas for
            
        Returns:
            Dict containing the complete results including final idea,
            evaluation, documentation, and workflow metadata
        """
        logger.info(f"Starting business idea generation for topic: '{topic}'")
        
        try:
            # Validate input
            if not topic or not topic.strip():
                raise ValueError("Topic cannot be empty")
            
            topic = topic.strip()
            
            # Execute the complete workflow through the coordinator
            result = await self.coordinator.coordinate_idea_generation(
                topic=topic,
                idea_generator=self.idea_generator,
                critique_agent=self.critique_agent,
                refinement_agent=self.refinement_agent,
                formatter_agent=self.output_formatter
            )
            
            # Log final results
            if result.get("success"):
                final_score = result.get("workflow_metadata", {}).get("final_score", 0.0)
                converged = result.get("workflow_metadata", {}).get("converged", False)
                logger.info(f"Idea generation completed successfully. Score: {final_score:.3f}, Converged: {converged}")
            else:
                logger.error(f"Idea generation failed: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Idea generation failed with exception: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "topic": topic
            }
    
    async def evaluate_existing_idea(self, idea: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate an existing business idea against PRD criteria.
        
        Args:
            idea: Business idea to evaluate
            
        Returns:
            Dict containing evaluation results and feedback
        """
        logger.info(f"Evaluating existing idea: {idea.get('title', 'Untitled')}")
        
        try:
            evaluation = await self.critique_agent.evaluate_idea(idea)
            return {
                "success": True,
                "idea": idea,
                "evaluation": evaluation
            }
        except Exception as e:
            logger.error(f"Idea evaluation failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea": idea
            }
    
    async def refine_existing_idea(self, idea: Dict[str, Any], evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Refine an existing business idea based on evaluation feedback.
        
        Args:
            idea: Business idea to refine
            evaluation: Evaluation results with feedback
            
        Returns:
            Dict containing refined idea
        """
        logger.info(f"Refining existing idea: {idea.get('title', 'Untitled')}")
        
        try:
            refined_idea = await self.refinement_agent.refine_idea(idea, evaluation)
            return {
                "success": True,
                "original_idea": idea,
                "refined_idea": refined_idea,
                "evaluation": evaluation
            }
        except Exception as e:
            logger.error(f"Idea refinement failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea": idea
            }
    
    async def format_idea_documentation(
        self, 
        idea: Dict[str, Any], 
        evaluation: Dict[str, Any],
        workflow_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Format an idea into standardized documentation.
        
        Args:
            idea: Business idea to document
            evaluation: Evaluation results
            workflow_metadata: Optional workflow information
            
        Returns:
            Dict containing formatted documentation
        """
        logger.info(f"Formatting documentation for: {idea.get('title', 'Untitled')}")
        
        try:
            if workflow_metadata is None:
                workflow_metadata = {
                    "total_iterations": 1,
                    "converged": evaluation.get("meets_threshold", False),
                    "final_score": evaluation.get("overall_score", 0.0)
                }
            
            formatted_output = await self.output_formatter.format_output(
                idea, 
                evaluation, 
                workflow_metadata
            )
            
            return {
                "success": True,
                "formatted_output": formatted_output
            }
        except Exception as e:
            logger.error(f"Documentation formatting failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "idea": idea
            }
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        Get the current status of the idea generation system.
        
        Returns:
            Dict containing system status information
        """
        return {
            "system_name": "Multi-Agent Idea Generation System",
            "version": "0.1.0",
            "model": self.config.default_model,
            "quality_threshold": self.config.quality_threshold,
            "max_iterations": self.config.max_iterations,
            "agents": {
                "coordinator": self.coordinator.name,
                "idea_generator": self.idea_generator.name,
                "critique_agent": self.critique_agent.name,
                "refinement_agent": self.refinement_agent.name,
                "output_formatter": self.output_formatter.name
            },
            "configuration": {
                "save_artifacts": self.config.save_artifacts,
                "artifacts_dir": self.config.artifacts_dir,
                "log_level": self.config.log_level,
                "debug_mode": self.config.debug_mode
            }
        }
    
    def get_workflow_summary(self, workflow_metadata: Dict[str, Any]) -> str:
        """
        Get a human-readable summary of the workflow process.
        
        Args:
            workflow_metadata: Metadata from workflow execution
            
        Returns:
            str: Formatted workflow summary
        """
        return self.coordinator.get_workflow_summary(workflow_metadata)
