# Multi-Agent Idea Generation System
## Product Requirements Document

### Overview
A multi-agent AI system designed to generate, critique, and refine business ideas through collaborative agent interactions. The system leverages internet research and iterative improvement cycles to produce well-evaluated, implementable business concepts.

### System Architecture

#### Core Components
- **Orchestrator Agent**: Central coordinator managing workflow and agent interactions
- **Idea Generator Agent**: Creates initial business concepts using internet research
- **Critique Agent**: Evaluates ideas against predefined metrics using external validation
- **Refinement Agent**: Improves ideas based on critique feedback
- **Output Formatter Agent**: Structures final output in standardized format

#### External Dependencies
- **Internet Tool (Google Search)**: Provides real-time market research and validation data
- **Gemini 2.5 Flash Preview Model**: Unified LLM across all agents

### Workflow Process

#### Phase 1: Initialization
1. **User Input Processing**
   - Receives focus area specification (industry-agnostic or domain-specific)
   - Orchestrator Agent parses requirements and initializes workflow

#### Phase 2: Idea Generation
2. **Research & Ideation**
   - Idea Generator Agent queries Google Search for market trends, gaps, and opportunities
   - Generates initial business concepts based on research findings
   - Applies self-reflection prompting to enhance idea quality

#### Phase 3: Evaluation Loop
3. **Critique Phase**
   - Critique Agent receives generated ideas
   - Conducts independent Google Search validation
   - Evaluates against comprehensive metrics (detailed below)
   - Provides structured feedback

4. **Refinement Cycle**
   - Refinement Agent processes critique feedback
   - Enhances ideas addressing identified weaknesses
   - May trigger additional critique rounds if needed

#### Phase 4: Iterative Improvement
5. **Feedback Loops**
   - **Primary Loop**: Idea Generator ↔ Critique Agent
   - **Secondary Loop**: Critique Agent ↔ Refinement Agent
   - Continues until ideas meet quality thresholds

#### Phase 5: Output Generation
6. **Final Formatting**
   - Output Formatter Agent structures approved ideas
   - Generates standardized markdown documentation

### Critique Evaluation Metrics

#### Practicality Criteria
- **Simplicity**: Easy to understand and implement
- **Effectiveness**: Demonstrates clear value proposition
- **Implementability**: Feasible with current technology and resources

#### Business Impact
- **Productivity Enhancement**: Measurable efficiency gains for employees
- **Task Optimization**: Reduces mundane, repetitive work
- **Daily Utility**: Provides consistent, ongoing value

#### Market Validation
- **Credibility**: Sufficient market evidence for stakeholder buy-in
- **Uniqueness**: Differentiated from existing internet-documented solutions
- **Adaptability**: Flexible across different business contexts

#### Quality Assurance
- **Non-theoretical**: Practical application over academic concepts
- **Employee-centric**: Designed for end-user adoption
- **Well-researched**: Backed by comprehensive market analysis

### Technical Specifications

#### Configuration Requirements
- **Environment Setup**: `.env` file with GOOGLE_API_KEY
- **Vertex AI**: GOOGLE_GENAI_USE_VERTEXAI = FALSE
- **Architecture**: MCP Agent Development Kit (Google)
- **Central Configuration**: config.py for unified settings management

#### Model Specifications
- **LLM**: Gemini-2.5-flash-preview-05-20 (mandatory across all agents)
- **Prompting Strategy**: Self-reflection techniques for enhanced reasoning
- **Search Integration**: Google Search API for real-time data access

### Output Format

#### Standardized Documentation
Each finalized idea includes:

**Use Case**
- Clear problem statement and target audience
- Specific application scenarios

**Impact**
- Quantifiable benefits and productivity gains
- Expected ROI and success metrics

**Complexity**
- Implementation difficulty assessment
- Resource requirements and timeline

**Uniqueness**
- Competitive differentiation
- Market positioning advantages

**Data Required to Begin**
- Essential datasets and information sources
- Initial setup and configuration needs

### Success Criteria
- Generate implementable business ideas within specified focus areas
- Achieve high critique scores across all evaluation metrics
- Produce comprehensive, actionable documentation
- Maintain quality consistency through iterative refinement cycles

### Risk Mitigation
- **Search Bias**: Multiple validation sources and cross-referencing
- **Infinite Loops**: Iteration limits and convergence criteria
- **Quality Control**: Multi-stage evaluation and refinement processes