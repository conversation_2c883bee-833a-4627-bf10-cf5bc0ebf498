#!/usr/bin/env python3
"""
Main entry point for the Multi-Agent Idea Generation System.

This script provides a command-line interface for generating business ideas
using the multi-agent system as specified in the PRD.
"""

import asyncio
import argparse
import json
import sys
from pathlib import Path
from typing import Dict, Any

from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

from src.idea_research_agent import IdeaResearchAgent
from src.idea_research_agent.utils.config import load_config
from src.idea_research_agent.utils.logger import setup_logger

console = Console()


def print_banner():
    """Print the application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║              Multi-Agent Idea Generation System              ║
║                                                              ║
║  Generate, Critique, and Refine Business Ideas with AI      ║
╚══════════════════════════════════════════════════════════════╝
"""
    console.print(banner, style="bold blue")


def print_system_status(agent: IdeaResearchAgent):
    """Print system status information."""
    status = agent.get_system_status()
    
    table = Table(title="System Status", show_header=True, header_style="bold magenta")
    table.add_column("Component", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("System", status["system_name"])
    table.add_row("Version", status["version"])
    table.add_row("Model", status["model"])
    table.add_row("Quality Threshold", str(status["quality_threshold"]))
    table.add_row("Max Iterations", str(status["max_iterations"]))
    
    console.print(table)


def print_evaluation_summary(evaluation: Dict[str, Any]):
    """Print evaluation summary in a formatted table."""
    table = Table(title="Evaluation Summary", show_header=True, header_style="bold magenta")
    table.add_column("Criteria", style="cyan")
    table.add_column("Score", style="green")
    table.add_column("Status", style="yellow")
    
    overall_score = evaluation.get("overall_score", 0.0)
    threshold = 0.8
    
    # Overall score
    status = "✅ PASS" if overall_score >= threshold else "❌ NEEDS IMPROVEMENT"
    table.add_row("Overall", f"{overall_score:.3f}", status)
    
    # Individual criteria
    criteria = [
        ("Practicality", evaluation.get("practicality", {}).get("score", 0.0)),
        ("Business Impact", evaluation.get("business_impact", {}).get("score", 0.0)),
        ("Market Validation", evaluation.get("market_validation", {}).get("score", 0.0)),
        ("Quality Assurance", evaluation.get("quality_assurance", {}).get("score", 0.0))
    ]
    
    for name, score in criteria:
        status = "✅" if score >= threshold else "⚠️"
        table.add_row(name, f"{score:.3f}", status)
    
    console.print(table)


def print_workflow_summary(workflow_metadata: Dict[str, Any]):
    """Print workflow execution summary."""
    total_iterations = workflow_metadata.get("total_iterations", 0)
    converged = workflow_metadata.get("converged", False)
    final_score = workflow_metadata.get("final_score", 0.0)
    
    panel_content = f"""
[bold]Workflow Execution Summary[/bold]

• Total Iterations: {total_iterations}
• Converged: {'✅ Yes' if converged else '❌ No (max iterations reached)'}
• Final Score: {final_score:.3f}
• Status: {'🎉 Success' if converged else '⚠️ Partial Success'}
"""
    
    console.print(Panel(panel_content, title="Workflow Results", border_style="green"))


async def generate_idea_interactive():
    """Interactive mode for idea generation."""
    console.print("\n[bold cyan]Interactive Idea Generation Mode[/bold cyan]")
    
    # Get topic from user
    topic = console.input("\n[bold]Enter the business domain or topic: [/bold]")
    
    if not topic.strip():
        console.print("[red]Error: Topic cannot be empty[/red]")
        return
    
    # Initialize the agent system
    console.print("\n[yellow]Initializing Multi-Agent System...[/yellow]")
    
    try:
        config = load_config()
        agent = IdeaResearchAgent(config)
        
        # Show system status
        print_system_status(agent)
        
        # Generate the idea with progress indicator
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Generating business idea...", total=None)
            
            result = await agent.generate_business_idea(topic)
        
        # Display results
        if result.get("success"):
            console.print("\n[bold green]✅ Idea Generation Completed Successfully![/bold green]")
            
            # Show evaluation summary
            evaluation = result.get("final_evaluation", {})
            print_evaluation_summary(evaluation)
            
            # Show workflow summary
            workflow_metadata = result.get("workflow_metadata", {})
            print_workflow_summary(workflow_metadata)
            
            # Show idea summary
            idea = result.get("final_idea", {})
            idea_panel = f"""
[bold]{idea.get('title', 'Untitled Idea')}[/bold]

{idea.get('description', 'No description available')}

[bold]Target Audience:[/bold] {idea.get('target_audience', 'Not specified')}
[bold]Value Proposition:[/bold] {idea.get('value_proposition', 'Not specified')}
"""
            console.print(Panel(idea_panel, title="Generated Business Idea", border_style="blue"))
            
            # Show export information if available
            formatted_output = result.get("formatted_output", {})
            export_info = formatted_output.get("export_info", {})
            
            if export_info and export_info.get("exported"):
                console.print(f"\n[green]📄 Documentation exported to: {export_info.get('file_path', 'Unknown')}[/green]")
            
        else:
            console.print(f"\n[red]❌ Idea generation failed: {result.get('error', 'Unknown error')}[/red]")
    
    except Exception as e:
        console.print(f"\n[red]❌ System error: {str(e)}[/red]")


async def generate_idea_batch(topic: str, output_file: str = None):
    """Batch mode for idea generation."""
    console.print(f"\n[bold cyan]Generating idea for topic: {topic}[/bold cyan]")
    
    try:
        config = load_config()
        agent = IdeaResearchAgent(config)
        
        result = await agent.generate_business_idea(topic)
        
        if result.get("success"):
            console.print("[green]✅ Idea generation completed successfully[/green]")
            
            # Save to file if specified
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                console.print(f"[green]Results saved to: {output_file}[/green]")
            else:
                # Print summary to console
                idea = result.get("final_idea", {})
                evaluation = result.get("final_evaluation", {})
                
                console.print(f"\n[bold]Title:[/bold] {idea.get('title', 'Untitled')}")
                console.print(f"[bold]Score:[/bold] {evaluation.get('overall_score', 0.0):.3f}")
                console.print(f"[bold]Description:[/bold] {idea.get('description', 'No description')}")
        else:
            console.print(f"[red]❌ Generation failed: {result.get('error', 'Unknown error')}[/red]")
            sys.exit(1)
    
    except Exception as e:
        console.print(f"[red]❌ System error: {str(e)}[/red]")
        sys.exit(1)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Multi-Agent Idea Generation System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Interactive mode
  python main.py --topic "healthcare AI"  # Batch mode
  python main.py --topic "fintech" --output results.json
        """
    )
    
    parser.add_argument(
        "--topic",
        type=str,
        help="Business domain or topic for idea generation"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        help="Output file for results (JSON format)"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        help="Path to configuration file (.env format)"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logger(level=log_level)
    
    # Print banner
    print_banner()
    
    # Run the appropriate mode
    if args.topic:
        # Batch mode
        asyncio.run(generate_idea_batch(args.topic, args.output))
    else:
        # Interactive mode
        asyncio.run(generate_idea_interactive())


if __name__ == "__main__":
    main()
