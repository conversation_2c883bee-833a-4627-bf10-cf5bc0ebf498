import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._width import Width<PERSON><PERSON><PERSON><PERSON>
    from ._smoothing import Smoothing<PERSON><PERSON><PERSON><PERSON>
    from ._shape import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._dash import <PERSON>Valida<PERSON>
    from ._color import ColorValidator
    from ._backoffsrc import BackoffsrcValidator
    from ._backoff import BackoffValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._width.WidthValidator",
            "._smoothing.SmoothingValidator",
            "._shape.ShapeValidator",
            "._dash.DashValidator",
            "._color.ColorValidator",
            "._backoffsrc.BackoffsrcValidator",
            "._backoff.BackoffValidator",
        ],
    )
