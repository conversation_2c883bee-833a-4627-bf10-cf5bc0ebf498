# Multi-Agent Idea Generation System

An AI-powered business idea generation system that uses multiple specialized agents to create, critique, and refine innovative business concepts through collaborative intelligence.

## 🎯 Overview

This system implements a sophisticated multi-agent workflow that:

1. **Generates** business ideas using internet research and market analysis
2. **Critiques** ideas against comprehensive evaluation criteria
3. **Refines** concepts iteratively until quality standards are met
4. **Formats** final ideas into professional documentation

## 🏗️ Architecture

The system consists of five specialized agents:

- **🎯 Orchestrator Agent**: Coordinates the entire workflow and manages iterations
- **💡 Idea Generator Agent**: Creates business concepts using internet research
- **🔍 Critique Agent**: Evaluates ideas against PRD criteria with external validation
- **🔧 Refinement Agent**: Improves ideas based on feedback
- **📄 Output Formatter Agent**: Creates standardized documentation

## 📋 Evaluation Criteria

Ideas are evaluated against four key criteria:

### 1. Practicality (25%)
- **Simplicity**: Easy to understand and implement
- **Effectiveness**: Actually solves the stated problem
- **Implementability**: Realistic with current technology

### 2. Business Impact (25%)
- **Productivity Enhancement**: Improves work efficiency
- **Task Optimization**: Streamlines business processes
- **Daily Utility**: Useful for regular operations

### 3. Market Validation (25%)
- **Credibility**: Evidence of market demand
- **Uniqueness**: Different from existing solutions
- **Adaptability**: Can evolve with market needs

### 4. Quality Assurance (25%)
- **Non-theoretical**: Practical rather than academic
- **Employee-centric**: Benefits actual users
- **Well-researched**: Based on solid market research

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- Google API Key for Gemini
- Google Search API Key (optional, for enhanced research)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd idea-research-agent
   ```

2. **Create virtual environment with UV**
   ```bash
   uv venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   uv pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

### Configuration

Create a `.env` file with the following variables:

```env
# Required
GOOGLE_API_KEY=your_google_api_key_here

# Optional (for enhanced research)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_custom_search_engine_id

# System Configuration
DEFAULT_MODEL=gemini-2.5-flash-preview-05-20
MAX_ITERATIONS=5
QUALITY_THRESHOLD=0.8
LOG_LEVEL=INFO
SAVE_ARTIFACTS=true
ARTIFACTS_DIR=./artifacts
```

## 💻 Usage

### Interactive Mode

Run the system interactively:

```bash
python main.py
```

This will prompt you for a business topic and guide you through the process.

### Batch Mode

Generate ideas programmatically:

```bash
# Generate idea for a specific topic
python main.py --topic "healthcare AI"

# Save results to file
python main.py --topic "fintech automation" --output results.json

# Enable verbose logging
python main.py --topic "education technology" --verbose
```

### Programmatic Usage

```python
import asyncio
from src.idea_research_agent import IdeaResearchAgent

async def main():
    # Initialize the system
    agent = IdeaResearchAgent()
    
    # Generate a business idea
    result = await agent.generate_business_idea("sustainable energy")
    
    if result["success"]:
        idea = result["final_idea"]
        evaluation = result["final_evaluation"]
        
        print(f"Generated: {idea['title']}")
        print(f"Score: {evaluation['overall_score']:.3f}")
        print(f"Description: {idea['description']}")
    else:
        print(f"Error: {result['error']}")

# Run the example
asyncio.run(main())
```

## 📊 Output Format

The system generates comprehensive documentation including:

### Idea Structure
```json
{
  "title": "AI-Powered Business Optimization Platform",
  "description": "Detailed description...",
  "problem_statement": "What problem does this solve?",
  "target_audience": "Who will use this?",
  "value_proposition": "What value does it provide?",
  "market_opportunity": "Market size and potential",
  "implementation_approach": "How to build it",
  "success_metrics": "How to measure success",
  "competitive_advantages": "What makes it unique"
}
```

### Evaluation Results
```json
{
  "overall_score": 0.85,
  "practicality": {"score": 0.8, "feedback": "..."},
  "business_impact": {"score": 0.9, "feedback": "..."},
  "market_validation": {"score": 0.8, "feedback": "..."},
  "quality_assurance": {"score": 0.9, "feedback": "..."},
  "improvement_suggestions": ["..."],
  "strengths": ["..."],
  "weaknesses": ["..."]
}
```

### Documentation Sections

Generated markdown reports include:

- **Use Case**: Problem statement, target audience, scenarios
- **Impact**: Quantifiable benefits, ROI, success metrics
- **Complexity**: Implementation difficulty, resource requirements
- **Uniqueness**: Competitive differentiation, market positioning
- **Data Required**: Essential datasets, information sources

## 🔧 Advanced Features

### Custom Evaluation

Evaluate existing ideas:

```python
# Evaluate an existing idea
evaluation = await agent.evaluate_existing_idea(your_idea)

# Refine based on feedback
refined_idea = await agent.refine_existing_idea(your_idea, evaluation)

# Generate documentation
docs = await agent.format_idea_documentation(refined_idea, evaluation)
```

### System Status

Check system configuration:

```python
status = agent.get_system_status()
print(f"Model: {status['model']}")
print(f"Quality Threshold: {status['quality_threshold']}")
```

## 📁 Project Structure

```
idea-research-agent/
├── src/idea_research_agent/
│   ├── agents/                 # Specialized agent implementations
│   │   ├── coordinator.py      # Orchestrator agent
│   │   ├── idea_generator.py   # Idea generation agent
│   │   ├── critique_agent.py   # Evaluation agent
│   │   ├── refinement_agent.py # Refinement agent
│   │   └── output_formatter.py # Documentation agent
│   ├── tools/                  # Tool implementations
│   │   ├── research_tools.py   # Web search and analysis
│   │   ├── analysis_tools.py   # Evaluation and critique
│   │   └── report_tools.py     # Documentation generation
│   ├── utils/                  # Utility modules
│   │   ├── config.py          # Configuration management
│   │   └── logger.py          # Logging utilities
│   └── agent.py               # Main agent class
├── main.py                    # CLI entry point
├── requirements.txt           # Dependencies
├── pyproject.toml            # Project configuration
└── README.md                 # This file
```

## 🧪 Testing

Run the test suite:

```bash
# Install development dependencies
uv pip install -e ".[dev]"

# Run tests
pytest

# Run with coverage
pytest --cov=idea_research_agent
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## 🆘 Support

For questions, issues, or contributions:

- Create an issue on GitHub
- Check the documentation
- Review the PRD for detailed requirements

## 🔮 Roadmap

- [ ] Enhanced market research capabilities
- [ ] Integration with additional AI providers
- [ ] Web-based user interface
- [ ] Batch processing for multiple topics
- [ ] Advanced visualization and reporting
- [ ] API endpoints for integration
